{"ecebed851493d783": {"name": "validate_number", "type": "function", "parent": null, "start_line": 5, "end_line": 11, "complexity": 2, "has_docstring": true, "content_length": 164, "token_count": 48, "document": "function validate_number"}, "f61d7d3d7d8cbafa": {"name": "format_result", "type": "function", "parent": null, "start_line": 13, "end_line": 17, "complexity": 2, "has_docstring": true, "content_length": 154, "token_count": 47, "document": "function format_result"}, "4e83f777cc50c47b": {"name": "validate_number", "type": "function", "parent": null, "start_line": 6, "end_line": 12, "complexity": 2, "has_docstring": true, "content_length": 164, "token_count": 48, "document": "function validate_number"}, "98ded7fa6618575a": {"name": "format_result", "type": "function", "parent": null, "start_line": 14, "end_line": 18, "complexity": 2, "has_docstring": true, "content_length": 154, "token_count": 47, "document": "function format_result"}, "da2e83ee5e32dd98": {"name": "add", "type": "function", "parent": null, "start_line": 5, "end_line": 7, "complexity": 1, "has_docstring": true, "content_length": 58, "token_count": 27, "document": "function add"}, "5194b77fc11af7b6": {"name": "subtract", "type": "function", "parent": null, "start_line": 9, "end_line": 11, "complexity": 1, "has_docstring": true, "content_length": 65, "token_count": 30, "document": "function subtract"}, "aef9eb87faf68850": {"name": "multiply", "type": "function", "parent": null, "start_line": 13, "end_line": 15, "complexity": 1, "has_docstring": true, "content_length": 68, "token_count": 27, "document": "function multiply"}, "ce11f314f88d2767": {"name": "divide", "type": "function", "parent": null, "start_line": 17, "end_line": 25, "complexity": 2, "has_docstring": true, "content_length": 196, "token_count": 59, "document": "function divide"}, "8e7b611a1780ef62": {"name": "modulo", "type": "function", "parent": null, "start_line": 27, "end_line": 35, "complexity": 2, "has_docstring": true, "content_length": 222, "token_count": 66, "document": "function modulo"}, "0266cca3db300d96": {"name": "power", "type": "function", "parent": null, "start_line": 37, "end_line": 45, "complexity": 2, "has_docstring": true, "content_length": 193, "token_count": 60, "document": "function power"}, "43914818f64a051d": {"name": "main", "type": "function", "parent": null, "start_line": 47, "end_line": 85, "complexity": 11, "has_docstring": true, "content_length": 1291, "token_count": 365, "document": "function main"}, "6c8374568999a4c3": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "9195eee0af03934d": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "function", "parent": null, "start_line": 3, "end_line": 16, "complexity": 2, "has_docstring": true, "content_length": 307, "token_count": 110, "document": "function <PERSON><PERSON><PERSON><PERSON>"}, "e7040a608e27f1cc": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "4f1a80d0821b1d50": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 14, "token_count": 6, "document": "import imports"}, "9d50a6f6fceb5d9d": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "fe04d483ff6a3707": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 31, "token_count": 10, "document": "import imports"}, "6b7c630a6e72e5ff": {"name": "FileScanner", "type": "class", "parent": null, "start_line": 6, "end_line": 7, "complexity": 0, "has_docstring": true, "content_length": 87, "token_count": 35, "document": "class FileScanner"}, "6fe6d99bfa9cd6d5": {"name": "__init__", "type": "function", "parent": null, "start_line": 9, "end_line": 10, "complexity": 1, "has_docstring": false, "content_length": 69, "token_count": 20, "document": "function __init__"}, "a4f960cfe61527a2": {"name": "scan", "type": "function", "parent": null, "start_line": 12, "end_line": 18, "complexity": 3, "has_docstring": false, "content_length": 253, "token_count": 55, "document": "function scan"}, "d9990a997205a855": {"name": "FileAnalyzer", "type": "class", "parent": null, "start_line": 20, "end_line": 21, "complexity": 0, "has_docstring": true, "content_length": 66, "token_count": 31, "document": "class FileAnalyzer"}, "2eda29a03e11be4b": {"name": "__init__", "type": "function", "parent": null, "start_line": 23, "end_line": 24, "complexity": 1, "has_docstring": false, "content_length": 69, "token_count": 23, "document": "function __init__"}, "c13833ac32e92c63": {"name": "analyze", "type": "function", "parent": null, "start_line": 26, "end_line": 33, "complexity": 3, "has_docstring": false, "content_length": 344, "token_count": 81, "document": "function analyze"}, "1f6cad0d52acb020": {"name": "DuplicateFinder", "type": "class", "parent": null, "start_line": 35, "end_line": 36, "complexity": 0, "has_docstring": true, "content_length": 69, "token_count": 26, "document": "class DuplicateFinder"}, "3e34ce8dd0bb9af7": {"name": "__init__", "type": "function", "parent": null, "start_line": 38, "end_line": 39, "complexity": 1, "has_docstring": false, "content_length": 69, "token_count": 23, "document": "function __init__"}, "5608b1a4dac63899": {"name": "find", "type": "function", "parent": null, "start_line": 41, "end_line": 50, "complexity": 4, "has_docstring": false, "content_length": 377, "token_count": 79, "document": "function find"}, "6b1090700be700c4": {"name": "ReportGenerator", "type": "class", "parent": null, "start_line": 52, "end_line": 53, "complexity": 0, "has_docstring": true, "content_length": 58, "token_count": 21, "document": "class ReportGenerator"}, "eb860faa4d201cd5": {"name": "__init__", "type": "function", "parent": null, "start_line": 55, "end_line": 56, "complexity": 1, "has_docstring": false, "content_length": 69, "token_count": 23, "document": "function __init__"}, "c8e12512785140b2": {"name": "generate", "type": "function", "parent": null, "start_line": 58, "end_line": 65, "complexity": 3, "has_docstring": false, "content_length": 358, "token_count": 85, "document": "function generate"}, "f0fa090775156e0a": {"name": "main", "type": "function", "parent": null, "start_line": 67, "end_line": 77, "complexity": 1, "has_docstring": false, "content_length": 385, "token_count": 81, "document": "function main"}, "abe2664263cc0be3": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "d0c7eddb53c7526e": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 31, "token_count": 8, "document": "import imports"}, "784560674863037b": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 29, "token_count": 10, "document": "import imports"}, "5528885284c061e5": {"name": "TextAnalyzer", "type": "class", "parent": null, "start_line": 5, "end_line": 8, "complexity": 0, "has_docstring": true, "content_length": 99, "token_count": 34, "document": "class TextAnalyzer"}, "0a02fd4592019536": {"name": "__init__", "type": "function", "parent": null, "start_line": 10, "end_line": 11, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 15, "document": "function __init__"}, "9e07ae31ee34a8a7": {"name": "count_words", "type": "function", "parent": null, "start_line": 14, "end_line": 24, "complexity": 1, "has_docstring": true, "content_length": 291, "token_count": 110, "document": "function count_words"}, "1a1737d41a0172f2": {"name": "count_sentences", "type": "function", "parent": null, "start_line": 27, "end_line": 37, "complexity": 1, "has_docstring": true, "content_length": 305, "token_count": 111, "document": "function count_sentences"}, "6338f6b28f078d63": {"name": "count_paragraphs", "type": "function", "parent": null, "start_line": 40, "end_line": 50, "complexity": 1, "has_docstring": true, "content_length": 314, "token_count": 117, "document": "function count_paragraphs"}, "f5067c31813840a4": {"name": "analyze_text", "type": "function", "parent": null, "start_line": 53, "end_line": 67, "complexity": 1, "has_docstring": true, "content_length": 525, "token_count": 156, "document": "function analyze_text"}, "b359bf7c66c948c6": {"name": "SentimentAnalyzer", "type": "class", "parent": null, "start_line": 69, "end_line": 72, "complexity": 0, "has_docstring": true, "content_length": 120, "token_count": 42, "document": "class SentimentAnalyzer"}, "afcf8044ff9d594a": {"name": "__init__", "type": "function", "parent": null, "start_line": 74, "end_line": 75, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 15, "document": "function __init__"}, "098a27f732db5497": {"name": "score_sentiment", "type": "function", "parent": null, "start_line": 78, "end_line": 89, "complexity": 1, "has_docstring": true, "content_length": 393, "token_count": 140, "document": "function score_sentiment"}, "e4cddf350a358f5e": {"name": "TextFormatter", "type": "class", "parent": null, "start_line": 91, "end_line": 94, "complexity": 0, "has_docstring": true, "content_length": 85, "token_count": 32, "document": "class TextFormatter"}, "7a59d0661196bc41": {"name": "__init__", "type": "function", "parent": null, "start_line": 96, "end_line": 97, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 15, "document": "function __init__"}, "44f74f15547f5f9d": {"name": "format_text", "type": "function", "parent": null, "start_line": 100, "end_line": 112, "complexity": 1, "has_docstring": true, "content_length": 376, "token_count": 130, "document": "function format_text"}, "5c20ef621ccf316c": {"name": "TextProcessor", "type": "class", "parent": null, "start_line": 114, "end_line": 117, "complexity": 0, "has_docstring": true, "content_length": 101, "token_count": 34, "document": "class TextProcessor"}, "acf14a5006a63c5c": {"name": "__init__", "type": "function", "parent": null, "start_line": 119, "end_line": 122, "complexity": 1, "has_docstring": false, "content_length": 162, "token_count": 41, "document": "function __init__"}, "77d32ab1cb371cb8": {"name": "process_text", "type": "function", "parent": null, "start_line": 124, "end_line": 137, "complexity": 1, "has_docstring": true, "content_length": 526, "token_count": 161, "document": "function process_text"}, "3c1493593a744856": {"name": "calculate_circle_area", "type": "function", "parent": null, "start_line": 3, "end_line": 19, "complexity": 3, "has_docstring": true, "content_length": 449, "token_count": 138, "document": "function calculate_circle_area"}, "384be46e20cf16e6": {"name": "reverse_string_and_count_vowels", "type": "function", "parent": null, "start_line": 1, "end_line": 19, "complexity": 3, "has_docstring": true, "content_length": 589, "token_count": 196, "document": "function reverse_string_and_count_vowels"}, "5515027945404b2b": {"name": "find_second_largest", "type": "function", "parent": null, "start_line": 1, "end_line": 14, "complexity": 2, "has_docstring": true, "content_length": 379, "token_count": 131, "document": "function find_second_largest"}, "22441518e5d5fd29": {"name": "bubble_sort", "type": "function", "parent": null, "start_line": 1, "end_line": 16, "complexity": 4, "has_docstring": true, "content_length": 426, "token_count": 161, "document": "function bubble_sort"}, "a37a4b4f4475a164": {"name": "<PERSON><PERSON>", "type": "class", "parent": null, "start_line": 1, "end_line": 13, "complexity": 0, "has_docstring": true, "content_length": 505, "token_count": 225, "document": "class Stack"}, "850ca67914b50fd6": {"name": "__init__", "type": "function", "parent": null, "start_line": 15, "end_line": 16, "complexity": 1, "has_docstring": false, "content_length": 47, "token_count": 18, "document": "function __init__"}, "d939a509ca3cfc1e": {"name": "push", "type": "function", "parent": null, "start_line": 18, "end_line": 20, "complexity": 1, "has_docstring": true, "content_length": 108, "token_count": 41, "document": "function push"}, "ef8399ae7279a6ec": {"name": "pop", "type": "function", "parent": null, "start_line": 22, "end_line": 26, "complexity": 2, "has_docstring": true, "content_length": 196, "token_count": 61, "document": "function pop"}, "9a7d21274fc94144": {"name": "peek", "type": "function", "parent": null, "start_line": 28, "end_line": 32, "complexity": 2, "has_docstring": true, "content_length": 204, "token_count": 64, "document": "function peek"}, "1bd4350399110b2e": {"name": "is_empty", "type": "function", "parent": null, "start_line": 34, "end_line": 36, "complexity": 1, "has_docstring": true, "content_length": 102, "token_count": 38, "document": "function is_empty"}, "94327b5c89a077d3": {"name": "size", "type": "function", "parent": null, "start_line": 38, "end_line": 40, "complexity": 1, "has_docstring": true, "content_length": 105, "token_count": 37, "document": "function size"}, "735a13278e71af9f": {"name": "Calculator", "type": "class", "parent": null, "start_line": 2, "end_line": 3, "complexity": 0, "has_docstring": true, "content_length": 89, "token_count": 31, "document": "class Calculator"}, "fe8f4fea26cccfd8": {"name": "__init__", "type": "function", "parent": null, "start_line": 5, "end_line": 6, "complexity": 1, "has_docstring": false, "content_length": 47, "token_count": 19, "document": "function __init__"}, "02125e67c2bae067": {"name": "add", "type": "function", "parent": null, "start_line": 8, "end_line": 15, "complexity": 2, "has_docstring": true, "content_length": 245, "token_count": 63, "document": "function add"}, "8044e0828832149d": {"name": "subtract", "type": "function", "parent": null, "start_line": 17, "end_line": 24, "complexity": 2, "has_docstring": true, "content_length": 258, "token_count": 66, "document": "function subtract"}, "295f6365d5ee2a46": {"name": "multiply", "type": "function", "parent": null, "start_line": 26, "end_line": 33, "complexity": 2, "has_docstring": true, "content_length": 255, "token_count": 63, "document": "function multiply"}, "f0328ba108d3613f": {"name": "divide", "type": "function", "parent": null, "start_line": 35, "end_line": 42, "complexity": 2, "has_docstring": true, "content_length": 252, "token_count": 66, "document": "function divide"}, "676bfa40fb0f1f7c": {"name": "power", "type": "function", "parent": null, "start_line": 44, "end_line": 51, "complexity": 2, "has_docstring": true, "content_length": 249, "token_count": 67, "document": "function power"}, "8d895db619d7fc29": {"name": "square_root", "type": "function", "parent": null, "start_line": 53, "end_line": 60, "complexity": 2, "has_docstring": true, "content_length": 258, "token_count": 67, "document": "function square_root"}, "ee85c460dcf4c0d6": {"name": "factorial", "type": "function", "parent": null, "start_line": 62, "end_line": 69, "complexity": 2, "has_docstring": true, "content_length": 257, "token_count": 64, "document": "function factorial"}, "a60f28c94453ad15": {"name": "calculate_square_root", "type": "function", "parent": null, "start_line": 3, "end_line": 19, "complexity": 2, "has_docstring": true, "content_length": 470, "token_count": 147, "document": "function calculate_square_root"}, "8953ad7fd26525e1": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "81027bc7b9c98c4f": {"name": "bubble_sort", "type": "function", "parent": null, "start_line": 3, "end_line": 10, "complexity": 4, "has_docstring": true, "content_length": 230, "token_count": 81, "document": "function bubble_sort"}, "66992c6686672794": {"name": "selection_sort", "type": "function", "parent": null, "start_line": 12, "end_line": 21, "complexity": 4, "has_docstring": true, "content_length": 286, "token_count": 88, "document": "function selection_sort"}, "cb477b919b70fff3": {"name": "sort_comparison", "type": "function", "parent": null, "start_line": 23, "end_line": 33, "complexity": 1, "has_docstring": true, "content_length": 400, "token_count": 108, "document": "function sort_comparison"}, "ba3fe2397987b84c": {"name": "BinarySearchTree", "type": "class", "parent": null, "start_line": 1, "end_line": 26, "complexity": 0, "has_docstring": true, "content_length": 1116, "token_count": 413, "document": "class BinarySearchTree"}, "5bcce8e47f9b8d63": {"name": "__init__", "type": "function", "parent": null, "start_line": 28, "end_line": 29, "complexity": 1, "has_docstring": false, "content_length": 48, "token_count": 18, "document": "function __init__"}, "30e0b4f4db77a440": {"name": "insert", "type": "function", "parent": null, "start_line": 31, "end_line": 51, "complexity": 7, "has_docstring": true, "content_length": 902, "token_count": 156, "document": "function insert"}, "dbc252a564b5d97f": {"name": "search", "type": "function", "parent": null, "start_line": 53, "end_line": 71, "complexity": 7, "has_docstring": true, "content_length": 698, "token_count": 125, "document": "function search"}, "b826b4f8a2778ac8": {"name": "delete", "type": "function", "parent": null, "start_line": 73, "end_line": 115, "complexity": 18, "has_docstring": true, "content_length": 1957, "token_count": 325, "document": "function delete"}, "e73821c9c09ec860": {"name": "in_order_traversal", "type": "function", "parent": null, "start_line": 117, "end_line": 131, "complexity": 5, "has_docstring": true, "content_length": 524, "token_count": 114, "document": "function in_order_traversal"}, "715d8c1eb155ccc2": {"name": "pre_order_traversal", "type": "function", "parent": null, "start_line": 133, "end_line": 145, "complexity": 4, "has_docstring": true, "content_length": 487, "token_count": 112, "document": "function pre_order_traversal"}, "c80aba2bfa518060": {"name": "post_order_traversal", "type": "function", "parent": null, "start_line": 147, "end_line": 161, "complexity": 5, "has_docstring": true, "content_length": 527, "token_count": 114, "document": "function post_order_traversal"}, "70545d0b0b40dde9": {"name": "find_minimum", "type": "function", "parent": null, "start_line": 163, "end_line": 171, "complexity": 3, "has_docstring": true, "content_length": 342, "token_count": 79, "document": "function find_minimum"}, "f9f7a07dfb406720": {"name": "find_maximum", "type": "function", "parent": null, "start_line": 173, "end_line": 181, "complexity": 3, "has_docstring": true, "content_length": 344, "token_count": 79, "document": "function find_maximum"}, "5b9a35363bd9ef00": {"name": "tree_height", "type": "function", "parent": null, "start_line": 183, "end_line": 190, "complexity": 2, "has_docstring": true, "content_length": 314, "token_count": 79, "document": "function tree_height"}, "adc1b0b42cf822d6": {"name": "Node", "type": "class", "parent": null, "start_line": 20, "end_line": 21, "complexity": 0, "has_docstring": true, "content_length": 63, "token_count": 28, "document": "class Node"}, "741b2ab313c54c68": {"name": "__init__", "type": "function", "parent": null, "start_line": 23, "end_line": 26, "complexity": 1, "has_docstring": false, "content_length": 124, "token_count": 32, "document": "function __init__"}, "505e14ac4de1ee65": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 16, "token_count": 6, "document": "import imports"}, "3b1669f1fd418368": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 12, "token_count": 6, "document": "import imports"}, "dcaa2078ff58fc7d": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "e83fad9c7f39d3a8": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "54ec913109467ca1": {"name": "WebScraperSimulator", "type": "class", "parent": null, "start_line": 6, "end_line": 10, "complexity": 0, "has_docstring": true, "content_length": 232, "token_count": 87, "document": "class WebScraperSimulator"}, "71195bcf2e62f11f": {"name": "__init__", "type": "function", "parent": null, "start_line": 12, "end_line": 16, "complexity": 1, "has_docstring": false, "content_length": 145, "token_count": 39, "document": "function __init__"}, "02f620fc6e8e1f51": {"name": "start", "type": "function", "parent": null, "start_line": 18, "end_line": 25, "complexity": 3, "has_docstring": false, "content_length": 267, "token_count": 57, "document": "function start"}, "794512c5362b2cc4": {"name": "scrape_url", "type": "function", "parent": null, "start_line": 27, "end_line": 40, "complexity": 3, "has_docstring": false, "content_length": 510, "token_count": 112, "document": "function scrape_url"}, "7dd91ecf90d137cd": {"name": "join", "type": "function", "parent": null, "start_line": 42, "end_line": 44, "complexity": 2, "has_docstring": false, "content_length": 85, "token_count": 21, "document": "function join"}, "53b557023d5b028c": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 8, "document": "import imports"}, "6dec2edb4f6a4c5c": {"name": "MatrixOperations", "type": "class", "parent": null, "start_line": 3, "end_line": 6, "complexity": 0, "has_docstring": true, "content_length": 133, "token_count": 44, "document": "class MatrixOperations"}, "ba85f26571476f21": {"name": "__init__", "type": "function", "parent": null, "start_line": 8, "end_line": 9, "complexity": 1, "has_docstring": false, "content_length": 60, "token_count": 20, "document": "function __init__"}, "697a3043dfd074b4": {"name": "add", "type": "function", "parent": null, "start_line": 11, "end_line": 21, "complexity": 1, "has_docstring": true, "content_length": 343, "token_count": 102, "document": "function add"}, "7c9eea64fcc4c0b8": {"name": "multiply", "type": "function", "parent": null, "start_line": 23, "end_line": 33, "complexity": 1, "has_docstring": true, "content_length": 370, "token_count": 106, "document": "function multiply"}, "6203e53de1cf9697": {"name": "determinant", "type": "function", "parent": null, "start_line": 35, "end_line": 42, "complexity": 1, "has_docstring": true, "content_length": 208, "token_count": 61, "document": "function determinant"}, "58b61d7814400faa": {"name": "inverse", "type": "function", "parent": null, "start_line": 44, "end_line": 51, "complexity": 1, "has_docstring": true, "content_length": 225, "token_count": 65, "document": "function inverse"}, "95ccccb642ed436b": {"name": "VectorOperations", "type": "class", "parent": null, "start_line": 53, "end_line": 56, "complexity": 0, "has_docstring": true, "content_length": 137, "token_count": 48, "document": "class VectorOperations"}, "ea05edb309d43baf": {"name": "__init__", "type": "function", "parent": null, "start_line": 58, "end_line": 59, "complexity": 1, "has_docstring": false, "content_length": 60, "token_count": 20, "document": "function __init__"}, "c3b0bc43a0601f44": {"name": "dot_product", "type": "function", "parent": null, "start_line": 61, "end_line": 71, "complexity": 1, "has_docstring": true, "content_length": 356, "token_count": 110, "document": "function dot_product"}, "3f7f3c841b12045a": {"name": "cross_product", "type": "function", "parent": null, "start_line": 73, "end_line": 83, "complexity": 1, "has_docstring": true, "content_length": 393, "token_count": 114, "document": "function cross_product"}, "ef2e780b95ddf068": {"name": "normalize", "type": "function", "parent": null, "start_line": 85, "end_line": 92, "complexity": 1, "has_docstring": true, "content_length": 245, "token_count": 74, "document": "function normalize"}, "e39beea57ddf29f5": {"name": "NumericalMethods", "type": "class", "parent": null, "start_line": 94, "end_line": 97, "complexity": 0, "has_docstring": true, "content_length": 166, "token_count": 60, "document": "class NumericalMethods"}, "8f101360c8a80a14": {"name": "__init__", "type": "function", "parent": null, "start_line": 99, "end_line": 100, "complexity": 1, "has_docstring": false, "content_length": 66, "token_count": 20, "document": "function __init__"}, "719ace9f0a65671f": {"name": "newton_rap<PERSON>on", "type": "function", "parent": null, "start_line": 102, "end_line": 120, "complexity": 3, "has_docstring": true, "content_length": 692, "token_count": 250, "document": "function newton_raphson"}, "78987a9d770d17ce": {"name": "simpson_integration", "type": "function", "parent": null, "start_line": 122, "end_line": 137, "complexity": 1, "has_docstring": true, "content_length": 601, "token_count": 234, "document": "function simpson_integration"}, "5640f6e619ea77df": {"name": "Queue", "type": "class", "parent": null, "start_line": 1, "end_line": 23, "complexity": 0, "has_docstring": true, "content_length": 664, "token_count": 259, "document": "class Queue"}, "64d0f933a4d2ae61": {"name": "__init__", "type": "function", "parent": null, "start_line": 25, "end_line": 27, "complexity": 1, "has_docstring": false, "content_length": 95, "token_count": 26, "document": "function __init__"}, "4df84f17310369e9": {"name": "enqueue", "type": "function", "parent": null, "start_line": 29, "end_line": 39, "complexity": 4, "has_docstring": true, "content_length": 390, "token_count": 87, "document": "function enqueue"}, "8899093f67498cbd": {"name": "dequeue", "type": "function", "parent": null, "start_line": 41, "end_line": 45, "complexity": 2, "has_docstring": true, "content_length": 197, "token_count": 59, "document": "function dequeue"}, "ec99534a0d53076b": {"name": "peek", "type": "function", "parent": null, "start_line": 47, "end_line": 51, "complexity": 2, "has_docstring": true, "content_length": 199, "token_count": 60, "document": "function peek"}, "c196c9393341c1c6": {"name": "is_empty", "type": "function", "parent": null, "start_line": 53, "end_line": 55, "complexity": 1, "has_docstring": true, "content_length": 101, "token_count": 38, "document": "function is_empty"}, "d923c209e5046611": {"name": "size", "type": "function", "parent": null, "start_line": 57, "end_line": 59, "complexity": 1, "has_docstring": true, "content_length": 107, "token_count": 37, "document": "function size"}, "a88c10efa0a846f5": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "8e52127db8f5ce78": {"name": "InputValidator", "type": "class", "parent": null, "start_line": 3, "end_line": 12, "complexity": 0, "has_docstring": true, "content_length": 432, "token_count": 157, "document": "class InputValidator"}, "64d202ee32099211": {"name": "__init__", "type": "function", "parent": null, "start_line": 14, "end_line": 18, "complexity": 1, "has_docstring": false, "content_length": 340, "token_count": 157, "document": "function __init__"}, "e256ea15ffc3f906": {"name": "validate_email", "type": "function", "parent": null, "start_line": 20, "end_line": 30, "complexity": 1, "has_docstring": true, "content_length": 309, "token_count": 101, "document": "function validate_email"}, "537be87928c99902": {"name": "validate_phone", "type": "function", "parent": null, "start_line": 32, "end_line": 42, "complexity": 1, "has_docstring": true, "content_length": 313, "token_count": 103, "document": "function validate_phone"}, "b19261a9fa2d09ce": {"name": "validate_credit_card", "type": "function", "parent": null, "start_line": 44, "end_line": 54, "complexity": 1, "has_docstring": true, "content_length": 361, "token_count": 116, "document": "function validate_credit_card"}, "cda58525e7d4c1bb": {"name": "validate_url", "type": "function", "parent": null, "start_line": 56, "end_line": 66, "complexity": 1, "has_docstring": true, "content_length": 276, "token_count": 97, "document": "function validate_url"}, "8583e7f9fd619d46": {"name": "validate_input", "type": "function", "parent": null, "start_line": 68, "end_line": 81, "complexity": 3, "has_docstring": true, "content_length": 496, "token_count": 145, "document": "function validate_input"}, "b985f68396715bc3": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "16ed149196376ca5": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 7, "document": "import imports"}, "774761c996b0ec44": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 29, "token_count": 10, "document": "import imports"}, "3b14bb3e4563225c": {"name": "PerformanceBenchmarking", "type": "class", "parent": null, "start_line": 5, "end_line": 8, "complexity": 0, "has_docstring": true, "content_length": 107, "token_count": 36, "document": "class PerformanceBenchmarking"}, "8ec3bbc1aa4aef90": {"name": "__init__", "type": "function", "parent": null, "start_line": 10, "end_line": 17, "complexity": 1, "has_docstring": false, "content_length": 249, "token_count": 61, "document": "function __init__"}, "98dba3a48d85b0c3": {"name": "start", "type": "function", "parent": null, "start_line": 19, "end_line": 29, "complexity": 1, "has_docstring": true, "content_length": 507, "token_count": 147, "document": "function start"}, "2c584233e578696a": {"name": "end", "type": "function", "parent": null, "start_line": 31, "end_line": 37, "complexity": 1, "has_docstring": true, "content_length": 284, "token_count": 72, "document": "function end"}, "1a972326e2741ec2": {"name": "get_performance_report", "type": "function", "parent": null, "start_line": 39, "end_line": 51, "complexity": 1, "has_docstring": true, "content_length": 485, "token_count": 119, "document": "function get_performance_report"}, "841c7f6055eabe2b": {"name": "timeit", "type": "function", "parent": null, "start_line": 53, "end_line": 63, "complexity": 1, "has_docstring": true, "content_length": 335, "token_count": 108, "document": "function timeit"}, "7bab09ca7d20d6d8": {"name": "profile", "type": "function", "parent": null, "start_line": 65, "end_line": 75, "complexity": 1, "has_docstring": true, "content_length": 365, "token_count": 119, "document": "function profile"}, "4f859e9fb69a84c3": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 8, "document": "import imports"}, "e6b1f7aa87d74da5": {"name": "helper_function", "type": "function", "parent": null, "start_line": 1, "end_line": 5, "complexity": 1, "has_docstring": true, "content_length": 132, "token_count": 47, "document": "function helper_function"}, "3265e596e4c22eeb": {"name": "main", "type": "function", "parent": null, "start_line": 7, "end_line": 15, "complexity": 2, "has_docstring": true, "content_length": 239, "token_count": 70, "document": "function main"}, "f1624ed4e022e075": {"name": "Calculator", "type": "class", "parent": null, "start_line": 1, "end_line": 2, "complexity": 0, "has_docstring": true, "content_length": 123, "token_count": 43, "document": "class Calculator"}, "fdc4571c420fe62b": {"name": "__init__", "type": "function", "parent": null, "start_line": 4, "end_line": 5, "complexity": 1, "has_docstring": false, "content_length": 47, "token_count": 19, "document": "function __init__"}, "0f7b749df90d7117": {"name": "add", "type": "function", "parent": null, "start_line": 7, "end_line": 9, "complexity": 1, "has_docstring": true, "content_length": 85, "token_count": 32, "document": "function add"}, "b53ec07afe4cab0d": {"name": "subtract", "type": "function", "parent": null, "start_line": 11, "end_line": 13, "complexity": 1, "has_docstring": true, "content_length": 98, "token_count": 35, "document": "function subtract"}, "2b45142b9cba65c6": {"name": "multiply", "type": "function", "parent": null, "start_line": 15, "end_line": 17, "complexity": 1, "has_docstring": true, "content_length": 95, "token_count": 32, "document": "function multiply"}, "bfeaa296973e8657": {"name": "divide", "type": "function", "parent": null, "start_line": 19, "end_line": 23, "complexity": 2, "has_docstring": true, "content_length": 166, "token_count": 51, "document": "function divide"}, "2506145a6100721f": {"name": "main", "type": "function", "parent": null, "start_line": 1, "end_line": 8, "complexity": 2, "has_docstring": true, "content_length": 209, "token_count": 63, "document": "function main"}, "b326f7b0cd8f5685": {"name": "calculate_result", "type": "function", "parent": null, "start_line": 1, "end_line": 18, "complexity": 2, "has_docstring": true, "content_length": 613, "token_count": 180, "document": "function calculate_result"}, "6297d304ee6fbe39": {"name": "main", "type": "function", "parent": null, "start_line": 1, "end_line": 4, "complexity": 1, "has_docstring": false, "content_length": 154, "token_count": 39, "document": "function main"}, "9479235d5549eb8f": {"name": "undefined_function", "type": "function", "parent": null, "start_line": 1, "end_line": 10, "complexity": 1, "has_docstring": true, "content_length": 227, "token_count": 90, "document": "function undefined_function"}, "6e8d8cca8da101f1": {"name": "Calculator", "type": "class", "parent": null, "start_line": 2, "end_line": 3, "complexity": 0, "has_docstring": true, "content_length": 98, "token_count": 37, "document": "class Calculator"}, "257c33dc4b64e930": {"name": "add", "type": "function", "parent": null, "start_line": 5, "end_line": 7, "complexity": 1, "has_docstring": true, "content_length": 109, "token_count": 40, "document": "function add"}, "cb6274f439b7feac": {"name": "multiply", "type": "function", "parent": null, "start_line": 9, "end_line": 11, "complexity": 1, "has_docstring": true, "content_length": 120, "token_count": 42, "document": "function multiply"}}