{"0f3eda3435d80340": {"name": "Calculator", "type": "class", "parent": null, "start_line": 5, "end_line": 6, "complexity": 0, "has_docstring": true, "content_length": 89, "token_count": 29, "document": "class Calculator"}, "5eceda099b8ddc8b": {"name": "__init__", "type": "function", "parent": null, "start_line": 8, "end_line": 9, "complexity": 1, "has_docstring": false, "content_length": 49, "token_count": 18, "document": "function __init__"}, "c0942f7c68486930": {"name": "add", "type": "function", "parent": null, "start_line": 11, "end_line": 15, "complexity": 1, "has_docstring": true, "content_length": 175, "token_count": 59, "document": "function add"}, "58d3838623ee2288": {"name": "subtract", "type": "function", "parent": null, "start_line": 17, "end_line": 21, "complexity": 1, "has_docstring": true, "content_length": 182, "token_count": 62, "document": "function subtract"}, "0badd36b4ae8a6f4": {"name": "multiply", "type": "function", "parent": null, "start_line": 23, "end_line": 27, "complexity": 1, "has_docstring": true, "content_length": 185, "token_count": 59, "document": "function multiply"}, "f0191843755b0667": {"name": "divide", "type": "function", "parent": null, "start_line": 29, "end_line": 35, "complexity": 2, "has_docstring": true, "content_length": 249, "token_count": 78, "document": "function divide"}, "41783b7fc87da08c": {"name": "get_history", "type": "function", "parent": null, "start_line": 37, "end_line": 39, "complexity": 1, "has_docstring": true, "content_length": 107, "token_count": 31, "document": "function get_history"}, "64fcd98d70b29f71": {"name": "clear_history", "type": "function", "parent": null, "start_line": 41, "end_line": 43, "complexity": 1, "has_docstring": true, "content_length": 102, "token_count": 30, "document": "function clear_history"}, "a2d03139888864b4": {"name": "calculate_factorial", "type": "function", "parent": null, "start_line": 46, "end_line": 52, "complexity": 4, "has_docstring": true, "content_length": 258, "token_count": 81, "document": "function calculate_factorial"}, "4808bc3883b1acf4": {"name": "fibonacci_sequence", "type": "function", "parent": null, "start_line": 55, "end_line": 68, "complexity": 5, "has_docstring": true, "content_length": 333, "token_count": 109, "document": "function fibonacci_sequence"}, "c458c0327844dfc0": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "374ab20ae95a9e6b": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 6, "document": "import imports"}, "b943b724f2a46bef": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 34, "token_count": 12, "document": "import imports"}, "ba52a5bf61085c40": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "2646a8f3e56263b6": {"name": "DataProcessor", "type": "class", "parent": null, "start_line": 11, "end_line": 12, "complexity": 0, "has_docstring": true, "content_length": 84, "token_count": 33, "document": "class DataProcessor"}, "c10b39fa8417bd81": {"name": "__init__", "type": "function", "parent": null, "start_line": 14, "end_line": 16, "complexity": 1, "has_docstring": false, "content_length": 108, "token_count": 32, "document": "function __init__"}, "b853bd209d7e4fb8": {"name": "read_json", "type": "function", "parent": null, "start_line": 18, "end_line": 28, "complexity": 3, "has_docstring": true, "content_length": 507, "token_count": 121, "document": "function read_json"}, "8fb14aebe4994d9d": {"name": "write_json", "type": "function", "parent": null, "start_line": 30, "end_line": 38, "complexity": 3, "has_docstring": true, "content_length": 389, "token_count": 103, "document": "function write_json"}, "2bc13d0a6e145a95": {"name": "read_csv", "type": "function", "parent": null, "start_line": 40, "end_line": 51, "complexity": 4, "has_docstring": true, "content_length": 555, "token_count": 129, "document": "function read_csv"}, "db6fa2231997f7ed": {"name": "filter_data", "type": "function", "parent": null, "start_line": 53, "end_line": 55, "complexity": 1, "has_docstring": true, "content_length": 198, "token_count": 68, "document": "function filter_data"}, "dd1509dc1d42bd6b": {"name": "aggregate_data", "type": "function", "parent": null, "start_line": 57, "end_line": 87, "complexity": 10, "has_docstring": true, "content_length": 1201, "token_count": 249, "document": "function aggregate_data"}, "5756d69914d06239": {"name": "validate_email", "type": "function", "parent": null, "start_line": 90, "end_line": 94, "complexity": 1, "has_docstring": true, "content_length": 211, "token_count": 78, "document": "function validate_email"}, "34c1af63106f38d4": {"name": "clean_text", "type": "function", "parent": null, "start_line": 97, "end_line": 107, "complexity": 1, "has_docstring": true, "content_length": 313, "token_count": 89, "document": "function clean_text"}, "f347987d86c5e865": {"name": "imports", "type": "import", "parent": null, "start_line": 92, "end_line": 92, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 7, "document": "import imports"}, "0efcafcd25218071": {"name": "imports", "type": "import", "parent": null, "start_line": 99, "end_line": 99, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 7, "document": "import imports"}, "5ce56f9562ac95a0": {"name": "power", "type": "function", "parent": null, "start_line": 71, "end_line": 73, "complexity": 1, "has_docstring": true, "content_length": 112, "token_count": 37, "document": "function power"}, "6758b85cc158e9b6": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "d7a81e6abb1572df": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "7903161a623a58a0": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "c799f41a16b198f1": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 7, "document": "import imports"}, "6fcfe2a756bb718e": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "da3dc71c29f88857": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 38, "token_count": 12, "document": "import imports"}, "496d99a39cc94f6d": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 49, "token_count": 13, "document": "import imports"}, "da7dca9b8aaa10f4": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 64, "token_count": 24, "document": "import imports"}, "4bb00b2eb69b89bf": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "faf8f72df636503d": {"name": "PathEncryption", "type": "class", "parent": null, "start_line": 12, "end_line": 13, "complexity": 0, "has_docstring": true, "content_length": 103, "token_count": 35, "document": "class PathEncryption"}, "d3245264b7bf1ce1": {"name": "__init__", "type": "function", "parent": null, "start_line": 15, "end_line": 17, "complexity": 1, "has_docstring": false, "content_length": 157, "token_count": 46, "document": "function __init__"}, "ccf091dae3642202": {"name": "_get_or_create_cipher", "type": "function", "parent": null, "start_line": 19, "end_line": 31, "complexity": 4, "has_docstring": true, "content_length": 478, "token_count": 128, "document": "function _get_or_create_cipher"}, "2a8a7808a0c01508": {"name": "encrypt_path", "type": "function", "parent": null, "start_line": 33, "end_line": 37, "complexity": 1, "has_docstring": true, "content_length": 256, "token_count": 75, "document": "function encrypt_path"}, "6b490877ee3c663a": {"name": "decrypt_path", "type": "function", "parent": null, "start_line": 39, "end_line": 43, "complexity": 1, "has_docstring": true, "content_length": 271, "token_count": 73, "document": "function decrypt_path"}, "47552c605cae72cb": {"name": "hash_content", "type": "function", "parent": null, "start_line": 45, "end_line": 49, "complexity": 1, "has_docstring": true, "content_length": 236, "token_count": 65, "document": "function hash_content"}, "5e9a066c39023446": {"name": "generate_chunk_id", "type": "function", "parent": null, "start_line": 52, "end_line": 57, "complexity": 1, "has_docstring": true, "content_length": 382, "token_count": 115, "document": "function generate_chunk_id"}, "c92986f10e77b7b4": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 6, "document": "import imports"}, "3afdbe59e6e427ca": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "9fc3c698d13a1db4": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 44, "token_count": 14, "document": "import imports"}, "12798eda6283f646": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 10, "document": "import imports"}, "bc6202677589c6b1": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "764d06c517712825": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "dd0608d884e88fad": {"name": "CodeChunk", "type": "class", "parent": null, "start_line": 12, "end_line": 21, "complexity": 0, "has_docstring": true, "content_length": 343, "token_count": 108, "document": "class CodeChunk"}, "67d1abb1eb8dd11f": {"name": "PythonParser", "type": "class", "parent": null, "start_line": 24, "end_line": 25, "complexity": 0, "has_docstring": true, "content_length": 63, "token_count": 27, "document": "class PythonParser"}, "d79fca214f42dc47": {"name": "__init__", "type": "function", "parent": null, "start_line": 27, "end_line": 28, "complexity": 1, "has_docstring": false, "content_length": 65, "token_count": 25, "document": "function __init__"}, "5e8f375e122f54c0": {"name": "parse_file", "type": "function", "parent": null, "start_line": 30, "end_line": 46, "complexity": 3, "has_docstring": true, "content_length": 582, "token_count": 133, "document": "function parse_file"}, "6480b218ba52908e": {"name": "_extract_chunks", "type": "function", "parent": null, "start_line": 48, "end_line": 56, "complexity": 5, "has_docstring": true, "content_length": 511, "token_count": 117, "document": "function _extract_chunks"}, "224c7a1ab5730f85": {"name": "_process_function", "type": "function", "parent": null, "start_line": 58, "end_line": 83, "complexity": 2, "has_docstring": true, "content_length": 946, "token_count": 204, "document": "function _process_function"}, "995f26ba47ac0a95": {"name": "_process_class", "type": "function", "parent": null, "start_line": 85, "end_line": 114, "complexity": 6, "has_docstring": true, "content_length": 1142, "token_count": 243, "document": "function _process_class"}, "19746e4247b9d25a": {"name": "_process_import", "type": "function", "parent": null, "start_line": 116, "end_line": 131, "complexity": 1, "has_docstring": true, "content_length": 507, "token_count": 119, "document": "function _process_import"}, "61867e3f21aef9e0": {"name": "_calculate_complexity", "type": "function", "parent": null, "start_line": 133, "end_line": 143, "complexity": 4, "has_docstring": true, "content_length": 465, "token_count": 112, "document": "function _calculate_complexity"}, "3114ea7e1fdecf32": {"name": "UniversalParser", "type": "class", "parent": null, "start_line": 146, "end_line": 147, "complexity": 0, "has_docstring": true, "content_length": 87, "token_count": 29, "document": "class UniversalParser"}, "cf1658912f3ccd74": {"name": "__init__", "type": "function", "parent": null, "start_line": 149, "end_line": 152, "complexity": 1, "has_docstring": false, "content_length": 92, "token_count": 27, "document": "function __init__"}, "3a31f184ff56e73b": {"name": "parse_file", "type": "function", "parent": null, "start_line": 154, "end_line": 162, "complexity": 2, "has_docstring": true, "content_length": 393, "token_count": 98, "document": "function parse_file"}, "79775ce56b7ed5b7": {"name": "_parse_as_text", "type": "function", "parent": null, "start_line": 164, "end_line": 188, "complexity": 3, "has_docstring": true, "content_length": 852, "token_count": 177, "document": "function _parse_as_text"}, "82b21bf9af677918": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "d5a9f5ebdacf2aad": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "aa33622ff17a617e": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 39, "token_count": 14, "document": "import imports"}, "fa686be50ac7e947": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 14, "token_count": 7, "document": "import imports"}, "3c833a80d746cbdb": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 63, "token_count": 14, "document": "import imports"}, "cccaffd59b9de5b5": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 16, "token_count": 6, "document": "import imports"}, "9f33761f9f6e99b5": {"name": "imports", "type": "import", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 50, "token_count": 13, "document": "import imports"}, "f969c0dfeaa4747d": {"name": "imports", "type": "import", "parent": null, "start_line": 13, "end_line": 13, "complexity": 0, "has_docstring": false, "content_length": 60, "token_count": 14, "document": "import imports"}, "11730379ef69b234": {"name": "imports", "type": "import", "parent": null, "start_line": 14, "end_line": 14, "complexity": 0, "has_docstring": false, "content_length": 56, "token_count": 14, "document": "import imports"}, "b6a87f4cd9055130": {"name": "imports", "type": "import", "parent": null, "start_line": 15, "end_line": 15, "complexity": 0, "has_docstring": false, "content_length": 34, "token_count": 9, "document": "import imports"}, "dd80d8a43605af26": {"name": "imports", "type": "import", "parent": null, "start_line": 16, "end_line": 16, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "c4747d0f77626a7d": {"name": "CodebaseAnalyzer", "type": "class", "parent": null, "start_line": 19, "end_line": 20, "complexity": 0, "has_docstring": true, "content_length": 115, "token_count": 41, "document": "class CodebaseAnalyzer"}, "8662172ddb0d74c4": {"name": "__init__", "type": "function", "parent": null, "start_line": 22, "end_line": 36, "complexity": 1, "has_docstring": false, "content_length": 474, "token_count": 108, "document": "function __init__"}, "b27c850b912fefa6": {"name": "analyze_path", "type": "function", "parent": null, "start_line": 38, "end_line": 102, "complexity": 10, "has_docstring": true, "content_length": 2563, "token_count": 508, "document": "function analyze_path"}, "294d2d770b47fbe8": {"name": "_process_file", "type": "function", "parent": null, "start_line": 104, "end_line": 152, "complexity": 12, "has_docstring": true, "content_length": 1937, "token_count": 367, "document": "function _process_file"}, "3207795ad8d0ad97": {"name": "_discover_files", "type": "function", "parent": null, "start_line": 154, "end_line": 174, "complexity": 8, "has_docstring": true, "content_length": 857, "token_count": 178, "document": "function _discover_files"}, "4c811f38fe30a556": {"name": "_should_process_file", "type": "function", "parent": null, "start_line": 176, "end_line": 195, "complexity": 5, "has_docstring": true, "content_length": 646, "token_count": 156, "document": "function _should_process_file"}, "69412a779392d211": {"name": "_should_ignore_path", "type": "function", "parent": null, "start_line": 197, "end_line": 207, "complexity": 4, "has_docstring": true, "content_length": 369, "token_count": 100, "document": "function _should_ignore_path"}, "77757a0c21310a53": {"name": "_reset_stats", "type": "function", "parent": null, "start_line": 209, "end_line": 217, "complexity": 1, "has_docstring": true, "content_length": 267, "token_count": 74, "document": "function _reset_stats"}, "17763794a42efd75": {"name": "analyze_single_file", "type": "function", "parent": null, "start_line": 219, "end_line": 257, "complexity": 4, "has_docstring": true, "content_length": 1506, "token_count": 289, "document": "function analyze_single_file"}, "56c95ceca61308f1": {"name": "get_similar_chunks", "type": "function", "parent": null, "start_line": 259, "end_line": 262, "complexity": 1, "has_docstring": true, "content_length": 209, "token_count": 68, "document": "function get_similar_chunks"}, "926bb65cd3f4ebe4": {"name": "get_chunk_details", "type": "function", "parent": null, "start_line": 264, "end_line": 292, "complexity": 4, "has_docstring": true, "content_length": 1051, "token_count": 214, "document": "function get_chunk_details"}, "0bdebf52ac71d345": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "9009bde70520b91d": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 37, "token_count": 10, "document": "import imports"}, "f054a0c29c8c73d8": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 34, "token_count": 9, "document": "import imports"}, "bb6a57d29331f3c8": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 32, "token_count": 9, "document": "import imports"}, "4240b4f3463c4b5c": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 9, "document": "import imports"}, "713afc27491c01d8": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 9, "document": "import imports"}, "c38c141a1a9051ae": {"name": "create_sample_code", "type": "function", "parent": null, "start_line": 15, "end_line": 211, "complexity": 3, "has_docstring": true, "content_length": 6296, "token_count": 1429, "document": "function create_sample_code"}, "694feaf2be24addd": {"name": "run_demo", "type": "function", "parent": null, "start_line": 214, "end_line": 327, "complexity": 7, "has_docstring": true, "content_length": 4376, "token_count": 998, "document": "function run_demo"}, "ccc009cafc7bb59f": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 8, "document": "import imports"}, "207f3380a59b3a51": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 39, "token_count": 12, "document": "import imports"}, "5ce862aed37210b0": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 14, "token_count": 6, "document": "import imports"}, "ea07d69881592614": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 15, "token_count": 7, "document": "import imports"}, "d24733d66d662632": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 10, "document": "import imports"}, "dd1804bd2185785d": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "435d071c5876e516": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 10, "document": "import imports"}, "6639a69b9ca0ad9a": {"name": "CodeEmbedding", "type": "class", "parent": null, "start_line": 13, "end_line": 18, "complexity": 0, "has_docstring": true, "content_length": 163, "token_count": 56, "document": "class CodeEmbedding"}, "faf8c7b11d1f340d": {"name": "EmbeddingGenerator", "type": "class", "parent": null, "start_line": 21, "end_line": 22, "complexity": 0, "has_docstring": true, "content_length": 82, "token_count": 30, "document": "class EmbeddingGenerator"}, "02e54c5e5b9337c7": {"name": "__init__", "type": "function", "parent": null, "start_line": 24, "end_line": 28, "complexity": 1, "has_docstring": false, "content_length": 186, "token_count": 53, "document": "function __init__"}, "c2fd33e5f835eb86": {"name": "_load_model", "type": "function", "parent": null, "start_line": 30, "end_line": 42, "complexity": 2, "has_docstring": true, "content_length": 623, "token_count": 138, "document": "function _load_model"}, "0a2210e741b4e7c5": {"name": "generate_embedding", "type": "function", "parent": null, "start_line": 44, "end_line": 77, "complexity": 2, "has_docstring": true, "content_length": 1271, "token_count": 253, "document": "function generate_embedding"}, "a526a06797ace360": {"name": "_prepare_text", "type": "function", "parent": null, "start_line": 79, "end_line": 98, "complexity": 5, "has_docstring": true, "content_length": 648, "token_count": 150, "document": "function _prepare_text"}, "4ea84cb53ca7e02b": {"name": "_simple_embedding", "type": "function", "parent": null, "start_line": 100, "end_line": 120, "complexity": 3, "has_docstring": true, "content_length": 923, "token_count": 225, "document": "function _simple_embedding"}, "3ace1ca55bddeb07": {"name": "batch_generate_embeddings", "type": "function", "parent": null, "start_line": 122, "end_line": 132, "complexity": 3, "has_docstring": true, "content_length": 447, "token_count": 93, "document": "function batch_generate_embeddings"}, "6305dce1ed4c2df8": {"name": "calculate_similarity", "type": "function", "parent": null, "start_line": 134, "end_line": 150, "complexity": 4, "has_docstring": true, "content_length": 631, "token_count": 150, "document": "function calculate_similarity"}, "3cc85cd1270375f0": {"name": "find_similar_chunks", "type": "function", "parent": null, "start_line": 152, "end_line": 165, "complexity": 2, "has_docstring": true, "content_length": 614, "token_count": 133, "document": "function find_similar_chunks"}, "9310c4e85ca93bc7": {"name": "imports", "type": "import", "parent": null, "start_line": 103, "end_line": 103, "complexity": 0, "has_docstring": false, "content_length": 22, "token_count": 7, "document": "import imports"}, "3c81974981147ccd": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 14, "token_count": 6, "document": "import imports"}, "35570a844b047015": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 44, "token_count": 14, "document": "import imports"}, "c354786fa583e0b4": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 41, "token_count": 13, "document": "import imports"}, "620476658e1a86af": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "a2acc0a4167c8773": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "f526761894538136": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "b3468ff439d14011": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 10, "document": "import imports"}, "ad9e4df2b77eacb1": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 52, "token_count": 13, "document": "import imports"}, "7afc9348473f1458": {"name": "ChunkFingerprint", "type": "class", "parent": null, "start_line": 14, "end_line": 25, "complexity": 0, "has_docstring": true, "content_length": 280, "token_count": 89, "document": "class ChunkFingerprint"}, "3f4f10eae2cd143f": {"name": "MerkleNode", "type": "class", "parent": null, "start_line": 28, "end_line": 29, "complexity": 0, "has_docstring": true, "content_length": 84, "token_count": 37, "document": "class MerkleNode"}, "0094f6d32fbd3a44": {"name": "__init__", "type": "function", "parent": null, "start_line": 31, "end_line": 36, "complexity": 1, "has_docstring": false, "content_length": 256, "token_count": 75, "document": "function __init__"}, "b847b37a4ea13cdc": {"name": "add_child", "type": "function", "parent": null, "start_line": 38, "end_line": 40, "complexity": 1, "has_docstring": true, "content_length": 122, "token_count": 44, "document": "function add_child"}, "cf6089aaf9de4d15": {"name": "add_chunk_hash", "type": "function", "parent": null, "start_line": 42, "end_line": 44, "complexity": 1, "has_docstring": true, "content_length": 136, "token_count": 46, "document": "function add_chunk_hash"}, "19ebaa000622ad29": {"name": "calculate_hash", "type": "function", "parent": null, "start_line": 46, "end_line": 61, "complexity": 2, "has_docstring": true, "content_length": 591, "token_count": 131, "document": "function calculate_hash"}, "4356e8dbd99b1097": {"name": "FingerprintManager", "type": "class", "parent": null, "start_line": 64, "end_line": 65, "complexity": 0, "has_docstring": true, "content_length": 95, "token_count": 37, "document": "class FingerprintManager"}, "debdb20b48b5e9eb": {"name": "__init__", "type": "function", "parent": null, "start_line": 67, "end_line": 72, "complexity": 1, "has_docstring": false, "content_length": 313, "token_count": 80, "document": "function __init__"}, "e68bb2a58e65c7e4": {"name": "load_fingerprints", "type": "function", "parent": null, "start_line": 74, "end_line": 87, "complexity": 5, "has_docstring": true, "content_length": 574, "token_count": 128, "document": "function load_fingerprints"}, "5a4f6e9a4f0b6503": {"name": "save_fingerprints", "type": "function", "parent": null, "start_line": 89, "end_line": 100, "complexity": 2, "has_docstring": true, "content_length": 456, "token_count": 121, "document": "function save_fingerprints"}, "80b1cd33fac31dc4": {"name": "create_fingerprint", "type": "function", "parent": null, "start_line": 102, "end_line": 125, "complexity": 1, "has_docstring": true, "content_length": 872, "token_count": 185, "document": "function create_fingerprint"}, "9eaeebf13b72d760": {"name": "update_fingerprints", "type": "function", "parent": null, "start_line": 127, "end_line": 167, "complexity": 8, "has_docstring": true, "content_length": 1687, "token_count": 338, "document": "function update_fingerprints"}, "747ffc7b772fa765": {"name": "build_merkle_tree", "type": "function", "parent": null, "start_line": 169, "end_line": 191, "complexity": 5, "has_docstring": true, "content_length": 907, "token_count": 207, "document": "function build_merkle_tree"}, "608564724ee07604": {"name": "_add_file_to_tree", "type": "function", "parent": null, "start_line": 193, "end_line": 220, "complexity": 6, "has_docstring": true, "content_length": 1122, "token_count": 246, "document": "function _add_file_to_tree"}, "dedc99f9c84934cc": {"name": "detect_changes", "type": "function", "parent": null, "start_line": 222, "end_line": 228, "complexity": 2, "has_docstring": true, "content_length": 303, "token_count": 88, "document": "function detect_changes"}, "44e834ce701a6e79": {"name": "get_file_changes", "type": "function", "parent": null, "start_line": 230, "end_line": 248, "complexity": 4, "has_docstring": true, "content_length": 741, "token_count": 159, "document": "function get_file_changes"}, "b730dd86b5c8638c": {"name": "imports", "type": "import", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 12, "token_count": 6, "document": "import imports"}, "9dc39c3f9d48cfeb": {"name": "imports", "type": "import", "parent": null, "start_line": 13, "end_line": 13, "complexity": 0, "has_docstring": false, "content_length": 32, "token_count": 9, "document": "import imports"}, "4bf73277b0b5f53c": {"name": "imports", "type": "import", "parent": null, "start_line": 14, "end_line": 14, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 9, "document": "import imports"}, "f29763c4ed215c09": {"name": "imports", "type": "import", "parent": null, "start_line": 15, "end_line": 15, "complexity": 0, "has_docstring": false, "content_length": 61, "token_count": 15, "document": "import imports"}, "30d737298ab7ca66": {"name": "imports", "type": "import", "parent": null, "start_line": 16, "end_line": 16, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "b31dd6e6c950d5cc": {"name": "imports", "type": "import", "parent": null, "start_line": 17, "end_line": 17, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "cb16a322a606adbf": {"name": "imports", "type": "import", "parent": null, "start_line": 18, "end_line": 18, "complexity": 0, "has_docstring": false, "content_length": 34, "token_count": 12, "document": "import imports"}, "850aeb9246280e5a": {"name": "imports", "type": "import", "parent": null, "start_line": 20, "end_line": 20, "complexity": 0, "has_docstring": false, "content_length": 37, "token_count": 10, "document": "import imports"}, "c98899d1ac37dacd": {"name": "imports", "type": "import", "parent": null, "start_line": 21, "end_line": 21, "complexity": 0, "has_docstring": false, "content_length": 34, "token_count": 9, "document": "import imports"}, "dc1e6b47eea6dc18": {"name": "imports", "type": "import", "parent": null, "start_line": 22, "end_line": 22, "complexity": 0, "has_docstring": false, "content_length": 41, "token_count": 10, "document": "import imports"}, "cce024bf0cd21454": {"name": "imports", "type": "import", "parent": null, "start_line": 23, "end_line": 23, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "dcbdeaf02c89f3bf": {"name": "cli", "type": "function", "parent": null, "start_line": 29, "end_line": 31, "complexity": 1, "has_docstring": true, "content_length": 95, "token_count": 37, "document": "function cli"}, "7ca3ca9da63a3a37": {"name": "analyze", "type": "function", "parent": null, "start_line": 39, "end_line": 70, "complexity": 4, "has_docstring": true, "content_length": 1142, "token_count": 266, "document": "function analyze"}, "6fd8f59bcb842040": {"name": "search", "type": "function", "parent": null, "start_line": 78, "end_line": 108, "complexity": 3, "has_docstring": true, "content_length": 1009, "token_count": 231, "document": "function search"}, "535c9dda32bce8ea": {"name": "list_chunks", "type": "function", "parent": null, "start_line": 115, "end_line": 144, "complexity": 6, "has_docstring": true, "content_length": 906, "token_count": 205, "document": "function list_chunks"}, "e880e48425abc28d": {"name": "stats", "type": "function", "parent": null, "start_line": 148, "end_line": 173, "complexity": 3, "has_docstring": true, "content_length": 951, "token_count": 227, "document": "function stats"}, "8300c59b1adca6f3": {"name": "reset", "type": "function", "parent": null, "start_line": 178, "end_line": 196, "complexity": 3, "has_docstring": true, "content_length": 585, "token_count": 132, "document": "function reset"}, "7c3fb1dea608041c": {"name": "display_analysis_results", "type": "function", "parent": null, "start_line": 199, "end_line": 222, "complexity": 3, "has_docstring": true, "content_length": 1068, "token_count": 272, "document": "function display_analysis_results"}, "201f902dc2ded47e": {"name": "display_search_results", "type": "function", "parent": null, "start_line": 225, "end_line": 251, "complexity": 3, "has_docstring": true, "content_length": 1061, "token_count": 267, "document": "function display_search_results"}, "9ba161c598372091": {"name": "display_chunk_list", "type": "function", "parent": null, "start_line": 254, "end_line": 282, "complexity": 3, "has_docstring": true, "content_length": 1148, "token_count": 292, "document": "function display_chunk_list"}, "5b76631b51c979b8": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 8, "document": "import imports"}, "3b1275ed9c563033": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 44, "token_count": 14, "document": "import imports"}, "0b2e33ad2c3b8407": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "a143742236f5c087": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "ec4d529a5268d2f3": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "1330516df732e696": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "44c3c6d58b61c09d": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 36, "token_count": 10, "document": "import imports"}, "9437e11f8811c6bb": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 9, "document": "import imports"}, "1b69f8b9003f3c5e": {"name": "VectorDatabase", "type": "class", "parent": null, "start_line": 13, "end_line": 14, "complexity": 0, "has_docstring": true, "content_length": 107, "token_count": 39, "document": "class VectorDatabase"}, "a69fc8e916dbec3a": {"name": "__init__", "type": "function", "parent": null, "start_line": 16, "end_line": 28, "complexity": 1, "has_docstring": false, "content_length": 474, "token_count": 115, "document": "function __init__"}, "d24bc3e5cc6f791c": {"name": "_load_embeddings", "type": "function", "parent": null, "start_line": 30, "end_line": 38, "complexity": 4, "has_docstring": true, "content_length": 367, "token_count": 86, "document": "function _load_embeddings"}, "acf9d14521882947": {"name": "_load_vectors", "type": "function", "parent": null, "start_line": 40, "end_line": 48, "complexity": 4, "has_docstring": true, "content_length": 356, "token_count": 87, "document": "function _load_vectors"}, "9eaf82250b036c12": {"name": "_save_embeddings", "type": "function", "parent": null, "start_line": 50, "end_line": 56, "complexity": 3, "has_docstring": true, "content_length": 290, "token_count": 77, "document": "function _save_embeddings"}, "d0b34feefd907ce5": {"name": "_save_vectors", "type": "function", "parent": null, "start_line": 58, "end_line": 64, "complexity": 3, "has_docstring": true, "content_length": 263, "token_count": 73, "document": "function _save_vectors"}, "1b820927a22759cd": {"name": "store_embeddings", "type": "function", "parent": null, "start_line": 66, "end_line": 106, "complexity": 5, "has_docstring": true, "content_length": 1667, "token_count": 307, "document": "function store_embeddings"}, "78d06cfdba3f21d9": {"name": "search_similar", "type": "function", "parent": null, "start_line": 108, "end_line": 145, "complexity": 7, "has_docstring": true, "content_length": 1483, "token_count": 261, "document": "function search_similar"}, "fa0fd566b759c9bf": {"name": "_cosine_similarity", "type": "function", "parent": null, "start_line": 147, "end_line": 159, "complexity": 4, "has_docstring": true, "content_length": 456, "token_count": 127, "document": "function _cosine_similarity"}, "23f000d2a961583c": {"name": "search_by_text", "type": "function", "parent": null, "start_line": 161, "end_line": 205, "complexity": 12, "has_docstring": true, "content_length": 1625, "token_count": 324, "document": "function search_by_text"}, "8f52ae01c273293a": {"name": "filter_by_metadata", "type": "function", "parent": null, "start_line": 207, "end_line": 231, "complexity": 6, "has_docstring": true, "content_length": 867, "token_count": 162, "document": "function filter_by_metadata"}, "7a377cc1ccc299a6": {"name": "get_chunk_by_id", "type": "function", "parent": null, "start_line": 233, "end_line": 251, "complexity": 3, "has_docstring": true, "content_length": 713, "token_count": 151, "document": "function get_chunk_by_id"}, "b843eefc9868a331": {"name": "delete_chunks", "type": "function", "parent": null, "start_line": 253, "end_line": 271, "complexity": 5, "has_docstring": true, "content_length": 672, "token_count": 144, "document": "function delete_chunks"}, "0b116fdfafe548bd": {"name": "get_statistics", "type": "function", "parent": null, "start_line": 273, "end_line": 305, "complexity": 4, "has_docstring": true, "content_length": 1226, "token_count": 252, "document": "function get_statistics"}, "151ef0ba122686c0": {"name": "reset_database", "type": "function", "parent": null, "start_line": 307, "end_line": 325, "complexity": 4, "has_docstring": true, "content_length": 601, "token_count": 126, "document": "function reset_database"}, "df39b0de3470279e": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "facc74cbd98543d2": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 51, "token_count": 12, "document": "import imports"}, "56b1a8f68d19c919": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 32, "token_count": 9, "document": "import imports"}, "796408eb3d27ee87": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 9, "document": "import imports"}, "604fbb5316057ce3": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 30, "token_count": 9, "document": "import imports"}, "65f4f298055295cd": {"name": "demo_code_generation", "type": "function", "parent": null, "start_line": 14, "end_line": 51, "complexity": 4, "has_docstring": true, "content_length": 1538, "token_count": 338, "document": "function demo_code_generation"}, "5657996162021a06": {"name": "demo_code_testing", "type": "function", "parent": null, "start_line": 54, "end_line": 113, "complexity": 4, "has_docstring": true, "content_length": 1656, "token_count": 435, "document": "function demo_code_testing"}, "96fa699fdc588c69": {"name": "demo_code_debugging", "type": "function", "parent": null, "start_line": 116, "end_line": 179, "complexity": 4, "has_docstring": true, "content_length": 2057, "token_count": 489, "document": "function demo_code_debugging"}, "dbc29e539f4edb86": {"name": "demo_interactive_features", "type": "function", "parent": null, "start_line": 182, "end_line": 195, "complexity": 1, "has_docstring": true, "content_length": 740, "token_count": 204, "document": "function demo_interactive_features"}, "e882dd7ca8541b15": {"name": "main", "type": "function", "parent": null, "start_line": 198, "end_line": 242, "complexity": 2, "has_docstring": true, "content_length": 2070, "token_count": 499, "document": "function main"}, "1384107af28990a8": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 17, "token_count": 6, "document": "import imports"}, "eabf24601009f969": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 15, "token_count": 6, "document": "import imports"}, "edfa54173eb48920": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "dbf8c94bdfb1394c": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 6, "document": "import imports"}, "fa0946c06ef092bb": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 6, "document": "import imports"}, "af7f33fc013f0b92": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "75a41cbd3d746a16": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "e692dcc307adf7ee": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 51, "token_count": 16, "document": "import imports"}, "a9dbb47f1eb87d06": {"name": "imports", "type": "import", "parent": null, "start_line": 11, "end_line": 11, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 10, "document": "import imports"}, "7a871051672a01aa": {"name": "imports", "type": "import", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 17, "token_count": 7, "document": "import imports"}, "4289d5bc2df6452a": {"name": "imports", "type": "import", "parent": null, "start_line": 13, "end_line": 13, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "8da3316b3cd263f2": {"name": "ExecutionResult", "type": "class", "parent": null, "start_line": 17, "end_line": 23, "complexity": 0, "has_docstring": true, "content_length": 162, "token_count": 51, "document": "class ExecutionResult"}, "3200c28304a94fe6": {"name": "SafeCodeExecutor", "type": "class", "parent": null, "start_line": 26, "end_line": 27, "complexity": 0, "has_docstring": true, "content_length": 79, "token_count": 32, "document": "class SafeCodeExecutor"}, "b7b97e3cae914d97": {"name": "__init__", "type": "function", "parent": null, "start_line": 29, "end_line": 40, "complexity": 1, "has_docstring": false, "content_length": 591, "token_count": 167, "document": "function __init__"}, "2a8b7b15ebbcfa8d": {"name": "validate_code", "type": "function", "parent": null, "start_line": 42, "end_line": 67, "complexity": 12, "has_docstring": true, "content_length": 1111, "token_count": 226, "document": "function validate_code"}, "0e0bb8df16c5becf": {"name": "execute_code", "type": "function", "parent": null, "start_line": 69, "end_line": 176, "complexity": 5, "has_docstring": true, "content_length": 3993, "token_count": 629, "document": "function execute_code"}, "eeff20c271ed9824": {"name": "execute_file", "type": "function", "parent": null, "start_line": 178, "end_line": 190, "complexity": 3, "has_docstring": true, "content_length": 461, "token_count": 102, "document": "function execute_file"}, "50de2a9a9ec83790": {"name": "test_function", "type": "function", "parent": null, "start_line": 192, "end_line": 222, "complexity": 3, "has_docstring": true, "content_length": 1070, "token_count": 254, "document": "function test_function"}, "bbd679b9dc6cd424": {"name": "CodeTester", "type": "class", "parent": null, "start_line": 225, "end_line": 226, "complexity": 0, "has_docstring": true, "content_length": 62, "token_count": 23, "document": "class CodeTester"}, "8e4f5818fbdddb46": {"name": "__init__", "type": "function", "parent": null, "start_line": 228, "end_line": 229, "complexity": 1, "has_docstring": false, "content_length": 66, "token_count": 21, "document": "function __init__"}, "124c62fcb59a84da": {"name": "generate_tests", "type": "function", "parent": null, "start_line": 231, "end_line": 261, "complexity": 7, "has_docstring": true, "content_length": 1340, "token_count": 275, "document": "function generate_tests"}, "4ec17d3dad5d1c4d": {"name": "test_code", "type": "function", "parent": null, "start_line": 263, "end_line": 291, "complexity": 3, "has_docstring": true, "content_length": 1130, "token_count": 222, "document": "function test_code"}, "205044c5e7283c3f": {"name": "validate_generated_code", "type": "function", "parent": null, "start_line": 293, "end_line": 330, "complexity": 9, "has_docstring": true, "content_length": 1381, "token_count": 274, "document": "function validate_generated_code"}, "6ae8f582b7364eac": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "725bd508290e7871": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "18ddd2599d5b3477": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 44, "token_count": 14, "document": "import imports"}, "664519131b853e15": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 32, "token_count": 9, "document": "import imports"}, "66992a977cfbac57": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 9, "document": "import imports"}, "649f390234c8f24a": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 30, "token_count": 9, "document": "import imports"}, "9a74d50d4f71ef28": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 9, "document": "import imports"}, "8578f91a64d85389": {"name": "imports", "type": "import", "parent": null, "start_line": 11, "end_line": 11, "complexity": 0, "has_docstring": false, "content_length": 37, "token_count": 10, "document": "import imports"}, "065cf901920cf180": {"name": "imports", "type": "import", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 34, "token_count": 9, "document": "import imports"}, "f3fbe3f0522cf59c": {"name": "imports", "type": "import", "parent": null, "start_line": 13, "end_line": 13, "complexity": 0, "has_docstring": false, "content_length": 50, "token_count": 15, "document": "import imports"}, "cc3a6a534d82a2e0": {"name": "imports", "type": "import", "parent": null, "start_line": 14, "end_line": 14, "complexity": 0, "has_docstring": false, "content_length": 54, "token_count": 14, "document": "import imports"}, "134d7a343119e970": {"name": "imports", "type": "import", "parent": null, "start_line": 15, "end_line": 15, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 9, "document": "import imports"}, "1a95250f2bfc16bb": {"name": "IntelligentCodeAssistant", "type": "class", "parent": null, "start_line": 18, "end_line": 19, "complexity": 0, "has_docstring": true, "content_length": 109, "token_count": 39, "document": "class IntelligentCodeAssistant"}, "abe923e26034db5e": {"name": "__init__", "type": "function", "parent": null, "start_line": 21, "end_line": 28, "complexity": 1, "has_docstring": false, "content_length": 302, "token_count": 70, "document": "function __init__"}, "891ac673270a64fd": {"name": "understand_request", "type": "function", "parent": null, "start_line": 30, "end_line": 57, "complexity": 2, "has_docstring": true, "content_length": 1020, "token_count": 237, "document": "function understand_request"}, "b7eb5059c39b4333": {"name": "_get_codebase_context", "type": "function", "parent": null, "start_line": 59, "end_line": 75, "complexity": 3, "has_docstring": true, "content_length": 842, "token_count": 205, "document": "function _get_codebase_context"}, "d23e42a12d409749": {"name": "generate_code", "type": "function", "parent": null, "start_line": 77, "end_line": 93, "complexity": 1, "has_docstring": true, "content_length": 679, "token_count": 147, "document": "function generate_code"}, "77ba423b8bf5cbf6": {"name": "modify_codebase", "type": "function", "parent": null, "start_line": 95, "end_line": 126, "complexity": 5, "has_docstring": true, "content_length": 1355, "token_count": 278, "document": "function modify_codebase"}, "ddcac7f7d32dd2bc": {"name": "_execute_change", "type": "function", "parent": null, "start_line": 128, "end_line": 184, "complexity": 8, "has_docstring": true, "content_length": 1861, "token_count": 369, "document": "function _execute_change"}, "a5f5489e8feb4ba4": {"name": "explain_code", "type": "function", "parent": null, "start_line": 186, "end_line": 205, "complexity": 3, "has_docstring": true, "content_length": 960, "token_count": 205, "document": "function explain_code"}, "15f553635eebd183": {"name": "debug_code", "type": "function", "parent": null, "start_line": 207, "end_line": 240, "complexity": 1, "has_docstring": true, "content_length": 915, "token_count": 213, "document": "function debug_code"}, "774e0c786c626166": {"name": "interactive_session", "type": "function", "parent": null, "start_line": 242, "end_line": 309, "complexity": 12, "has_docstring": true, "content_length": 3236, "token_count": 639, "document": "function interactive_session"}, "d42328854bbe18d9": {"name": "_display_generation_result", "type": "function", "parent": null, "start_line": 311, "end_line": 324, "complexity": 3, "has_docstring": true, "content_length": 686, "token_count": 161, "document": "function _display_generation_result"}, "5314e18cccdde06a": {"name": "_display_modification_result", "type": "function", "parent": null, "start_line": 326, "end_line": 341, "complexity": 3, "has_docstring": true, "content_length": 661, "token_count": 154, "document": "function _display_modification_result"}, "cd3775fb51f08d1d": {"name": "_display_debug_result", "type": "function", "parent": null, "start_line": 343, "end_line": 350, "complexity": 2, "has_docstring": true, "content_length": 347, "token_count": 87, "document": "function _display_debug_result"}, "0290a9ec4f26d18e": {"name": "_display_execution_result", "type": "function", "parent": null, "start_line": 352, "end_line": 359, "complexity": 3, "has_docstring": true, "content_length": 388, "token_count": 92, "document": "function _display_execution_result"}, "745a5b7a9ce41d24": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "2c3cddc0b6f14f0a": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 15, "token_count": 6, "document": "import imports"}, "16418d71904ff584": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 44, "token_count": 14, "document": "import imports"}, "59b265aab07d1247": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 10, "document": "import imports"}, "0e514199a5f93706": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "09f9408c66e4ed6d": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "05f7e12590397921": {"name": "CodeChange", "type": "class", "parent": null, "start_line": 12, "end_line": 19, "complexity": 0, "has_docstring": true, "content_length": 246, "token_count": 82, "document": "class CodeChange"}, "2985f939629dbb28": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "class", "parent": null, "start_line": 22, "end_line": 23, "complexity": 0, "has_docstring": true, "content_length": 58, "token_count": 29, "document": "class LLMProvider"}, "90ec5545eb0528f4": {"name": "generate_code", "type": "function", "parent": null, "start_line": 25, "end_line": 27, "complexity": 1, "has_docstring": true, "content_length": 158, "token_count": 46, "document": "function generate_code"}, "7bdf2de59600a3a9": {"name": "analyze_code", "type": "function", "parent": null, "start_line": 29, "end_line": 31, "complexity": 1, "has_docstring": true, "content_length": 152, "token_count": 47, "document": "function analyze_code"}, "9a8794004fc1aa9a": {"name": "OpenAIProvider", "type": "class", "parent": null, "start_line": 34, "end_line": 35, "complexity": 0, "has_docstring": true, "content_length": 68, "token_count": 30, "document": "class OpenAIProvider"}, "714d66395ef3c9b1": {"name": "__init__", "type": "function", "parent": null, "start_line": 37, "end_line": 39, "complexity": 2, "has_docstring": false, "content_length": 158, "token_count": 51, "document": "function __init__"}, "369b72cb3877dcb4": {"name": "generate_code", "type": "function", "parent": null, "start_line": 41, "end_line": 91, "complexity": 5, "has_docstring": true, "content_length": 1748, "token_count": 358, "document": "function generate_code"}, "1a84d10ac79c2113": {"name": "analyze_code", "type": "function", "parent": null, "start_line": 93, "end_line": 139, "complexity": 4, "has_docstring": true, "content_length": 1439, "token_count": 314, "document": "function analyze_code"}, "e07b1a07366a3a0b": {"name": "_fallback_response", "type": "function", "parent": null, "start_line": 141, "end_line": 257, "complexity": 7, "has_docstring": true, "content_length": 3732, "token_count": 1021, "document": "function _fallback_response"}, "34ea4b1644549999": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "class", "parent": null, "start_line": 260, "end_line": 261, "complexity": 0, "has_docstring": true, "content_length": 96, "token_count": 40, "document": "class OllamaProvider"}, "8f4d6103d5e0cfc1": {"name": "__init__", "type": "function", "parent": null, "start_line": 263, "end_line": 265, "complexity": 1, "has_docstring": false, "content_length": 151, "token_count": 48, "document": "function __init__"}, "2451129b2970a40b": {"name": "generate_code", "type": "function", "parent": null, "start_line": 267, "end_line": 322, "complexity": 5, "has_docstring": true, "content_length": 1774, "token_count": 366, "document": "function generate_code"}, "0c5ce117c80594dc": {"name": "analyze_code", "type": "function", "parent": null, "start_line": 324, "end_line": 361, "complexity": 3, "has_docstring": true, "content_length": 1062, "token_count": 240, "document": "function analyze_code"}, "28a3dad1b78721bd": {"name": "_fallback_local_response", "type": "function", "parent": null, "start_line": 363, "end_line": 377, "complexity": 1, "has_docstring": true, "content_length": 504, "token_count": 154, "document": "function _fallback_local_response"}, "87b6368f0ae8a71e": {"name": "LLMManager", "type": "class", "parent": null, "start_line": 380, "end_line": 381, "complexity": 0, "has_docstring": true, "content_length": 70, "token_count": 33, "document": "class LLMManager"}, "ba16a740039cea82": {"name": "__init__", "type": "function", "parent": null, "start_line": 383, "end_line": 388, "complexity": 1, "has_docstring": false, "content_length": 207, "token_count": 56, "document": "function __init__"}, "c37575e10512d8b3": {"name": "set_provider", "type": "function", "parent": null, "start_line": 390, "end_line": 395, "complexity": 2, "has_docstring": true, "content_length": 265, "token_count": 65, "document": "function set_provider"}, "1a527db3ac5b977e": {"name": "generate_code", "type": "function", "parent": null, "start_line": 397, "end_line": 401, "complexity": 2, "has_docstring": true, "content_length": 328, "token_count": 81, "document": "function generate_code"}, "ef9a06f9866fbf53": {"name": "analyze_code", "type": "function", "parent": null, "start_line": 403, "end_line": 407, "complexity": 2, "has_docstring": true, "content_length": 318, "token_count": 83, "document": "function analyze_code"}, "997373246e775b22": {"name": "plan_changes", "type": "function", "parent": null, "start_line": 409, "end_line": 456, "complexity": 11, "has_docstring": true, "content_length": 1987, "token_count": 407, "document": "function plan_changes"}, "f3792dbb0b7325d5": {"name": "imports", "type": "import", "parent": null, "start_line": 15, "end_line": 15, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 9, "document": "import imports"}, "886607177a8cc70c": {"name": "imports", "type": "import", "parent": null, "start_line": 16, "end_line": 16, "complexity": 0, "has_docstring": false, "content_length": 61, "token_count": 15, "document": "import imports"}, "787e0a62b0e9f6af": {"name": "imports", "type": "import", "parent": null, "start_line": 17, "end_line": 17, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 8, "document": "import imports"}, "7c03bfcdb26ea9ed": {"name": "imports", "type": "import", "parent": null, "start_line": 18, "end_line": 18, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "d8cd3069f830cfbf": {"name": "imports", "type": "import", "parent": null, "start_line": 19, "end_line": 19, "complexity": 0, "has_docstring": false, "content_length": 34, "token_count": 12, "document": "import imports"}, "be67ef136d144131": {"name": "imports", "type": "import", "parent": null, "start_line": 21, "end_line": 21, "complexity": 0, "has_docstring": false, "content_length": 37, "token_count": 10, "document": "import imports"}, "d1bd69a0b5e8e1e9": {"name": "imports", "type": "import", "parent": null, "start_line": 22, "end_line": 22, "complexity": 0, "has_docstring": false, "content_length": 34, "token_count": 9, "document": "import imports"}, "83d55076936d347b": {"name": "imports", "type": "import", "parent": null, "start_line": 23, "end_line": 23, "complexity": 0, "has_docstring": false, "content_length": 41, "token_count": 10, "document": "import imports"}, "1cdb64b5fd3b2b44": {"name": "imports", "type": "import", "parent": null, "start_line": 24, "end_line": 24, "complexity": 0, "has_docstring": false, "content_length": 51, "token_count": 12, "document": "import imports"}, "4904e59b53b66d7b": {"name": "imports", "type": "import", "parent": null, "start_line": 25, "end_line": 25, "complexity": 0, "has_docstring": false, "content_length": 38, "token_count": 12, "document": "import imports"}, "dbc75de0ad27f1dc": {"name": "imports", "type": "import", "parent": null, "start_line": 26, "end_line": 26, "complexity": 0, "has_docstring": false, "content_length": 54, "token_count": 14, "document": "import imports"}, "7e0058526a02a0b3": {"name": "imports", "type": "import", "parent": null, "start_line": 27, "end_line": 27, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "c5c2f3cc921b916d": {"name": "cli", "type": "function", "parent": null, "start_line": 33, "end_line": 35, "complexity": 1, "has_docstring": true, "content_length": 95, "token_count": 37, "document": "function cli"}, "1aaf77685c680e05": {"name": "analyze", "type": "function", "parent": null, "start_line": 43, "end_line": 74, "complexity": 4, "has_docstring": true, "content_length": 1142, "token_count": 266, "document": "function analyze"}, "f6ed779fd60c6462": {"name": "search", "type": "function", "parent": null, "start_line": 82, "end_line": 112, "complexity": 3, "has_docstring": true, "content_length": 1009, "token_count": 231, "document": "function search"}, "91d953b622d913a9": {"name": "list_chunks", "type": "function", "parent": null, "start_line": 119, "end_line": 148, "complexity": 6, "has_docstring": true, "content_length": 906, "token_count": 205, "document": "function list_chunks"}, "d589593a03fce489": {"name": "stats", "type": "function", "parent": null, "start_line": 152, "end_line": 177, "complexity": 3, "has_docstring": true, "content_length": 951, "token_count": 227, "document": "function stats"}, "09ef2d0a3a126b2d": {"name": "reset", "type": "function", "parent": null, "start_line": 182, "end_line": 200, "complexity": 3, "has_docstring": true, "content_length": 585, "token_count": 132, "document": "function reset"}, "1c33a699ae19c180": {"name": "generate", "type": "function", "parent": null, "start_line": 207, "end_line": 225, "complexity": 2, "has_docstring": true, "content_length": 665, "token_count": 144, "document": "function generate"}, "9702a4cf527267a5": {"name": "modify", "type": "function", "parent": null, "start_line": 233, "end_line": 255, "complexity": 3, "has_docstring": true, "content_length": 919, "token_count": 210, "document": "function modify"}, "8bf53c060086ad20": {"name": "explain", "type": "function", "parent": null, "start_line": 262, "end_line": 271, "complexity": 1, "has_docstring": true, "content_length": 406, "token_count": 103, "document": "function explain"}, "718037444ddcbc2e": {"name": "debug", "type": "function", "parent": null, "start_line": 278, "end_line": 287, "complexity": 2, "has_docstring": true, "content_length": 364, "token_count": 91, "document": "function debug"}, "2aac7c72d3055507": {"name": "test", "type": "function", "parent": null, "start_line": 292, "end_line": 306, "complexity": 3, "has_docstring": true, "content_length": 532, "token_count": 130, "document": "function test"}, "0d60a4e683ad4c75": {"name": "chat", "type": "function", "parent": null, "start_line": 312, "end_line": 317, "complexity": 1, "has_docstring": true, "content_length": 227, "token_count": 57, "document": "function chat"}, "daa716509d9adf2e": {"name": "display_analysis_results", "type": "function", "parent": null, "start_line": 320, "end_line": 343, "complexity": 3, "has_docstring": true, "content_length": 1068, "token_count": 272, "document": "function display_analysis_results"}, "29a70bc72d179864": {"name": "display_search_results", "type": "function", "parent": null, "start_line": 346, "end_line": 372, "complexity": 3, "has_docstring": true, "content_length": 1061, "token_count": 267, "document": "function display_search_results"}, "8d975e54eebc93d9": {"name": "display_chunk_list", "type": "function", "parent": null, "start_line": 375, "end_line": 403, "complexity": 3, "has_docstring": true, "content_length": 1148, "token_count": 292, "document": "function display_chunk_list"}, "b328818bed5c21b0": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 17, "token_count": 6, "document": "import imports"}, "4f9723ad783a39ad": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "e9d42ea1221a7128": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 32, "token_count": 9, "document": "import imports"}, "0f3cd7ac0a9b3e08": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 9, "document": "import imports"}, "bc7e25cf29db3b21": {"name": "run_command", "type": "function", "parent": null, "start_line": 13, "end_line": 32, "complexity": 5, "has_docstring": true, "content_length": 850, "token_count": 211, "document": "function run_command"}, "f3db6426bfbd7f2b": {"name": "main", "type": "function", "parent": null, "start_line": 35, "end_line": 113, "complexity": 1, "has_docstring": true, "content_length": 2831, "token_count": 732, "document": "function main"}, "01e76bffef81f837": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "1c7a557a1658dd07": {"name": "divide", "type": "function", "parent": null, "start_line": 3, "end_line": 18, "complexity": 2, "has_docstring": true, "content_length": 443, "token_count": 159, "document": "function divide"}, "f93c321f3e2314db": {"name": "main", "type": "function", "parent": null, "start_line": 20, "end_line": 23, "complexity": 1, "has_docstring": false, "content_length": 117, "token_count": 44, "document": "function main"}, "9ed8667c7404970a": {"name": "add", "type": "function", "parent": null, "start_line": 3, "end_line": 4, "complexity": 1, "has_docstring": false, "content_length": 31, "token_count": 15, "document": "function add"}, "5a20093a223d4257": {"name": "subtract", "type": "function", "parent": null, "start_line": 6, "end_line": 7, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 15, "document": "function subtract"}, "9b1d4e57a80af867": {"name": "multiply", "type": "function", "parent": null, "start_line": 9, "end_line": 10, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 15, "document": "function multiply"}, "41d8c28d624fb03a": {"name": "divide", "type": "function", "parent": null, "start_line": 1, "end_line": 17, "complexity": 2, "has_docstring": true, "content_length": 448, "token_count": 165, "document": "function divide"}}