"""
Agentic coder that can read entire directories and make intelligent multi-file modifications.
"""

import os
from pathlib import Path
from typing import List, Dict, Any
from dataclasses import dataclass
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.tree import Tree
from rich.syntax import Syntax

from analyzer import <PERSON>base<PERSON>nalyzer
from llm_integration import <PERSON><PERSON>ana<PERSON>, <PERSON><PERSON>hange
from code_executor import SafeCodeExecutor


@dataclass
class FileContent:
    """Represents a file and its content."""
    path: str
    content: str
    language: str
    size: int
    lines: int


@dataclass
class ModificationPlan:
    """Represents a plan for modifying the codebase."""
    description: str
    files_to_modify: List[str]
    files_to_create: List[str]
    files_to_delete: List[str]
    modifications: List[CodeChange]
    reasoning: str


class AgenticCoder:
    """An agentic coder that can understand and modify entire codebases."""

    def __init__(self):
        self.console = Console()
        self.analyzer = CodebaseAnalyzer()
        self.llm_manager = LLMManager()
        self.executor = SafeCodeExecutor()
        self.codebase_files: Dict[str, FileContent] = {}
        self.codebase_structure: Dict[str, Any] = {}

    def read_directory(self, directory_path: Path, max_file_size: int = 100000) -> Dict[str, FileContent]:
        """Read and analyze an entire directory structure."""
        self.console.print(f"[bold blue]📁 Reading directory: {directory_path}[/bold blue]")

        files = {}
        total_files = 0
        total_size = 0

        for root, dirs, filenames in os.walk(directory_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if not self._should_ignore_path(Path(root) / d)]

            for filename in filenames:
                file_path = Path(root) / filename

                if self._should_ignore_path(file_path):
                    continue

                try:
                    # Check file size
                    file_size = file_path.stat().st_size
                    if file_size > max_file_size:
                        self.console.print(f"[yellow]Skipping large file: {file_path} ({file_size} bytes)[/yellow]")
                        continue

                    # Read file content
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    # Determine language
                    language = self._detect_language(file_path)

                    # Store relative path
                    rel_path = str(file_path.relative_to(directory_path))

                    files[rel_path] = FileContent(
                        path=rel_path,
                        content=content,
                        language=language,
                        size=file_size,
                        lines=len(content.splitlines())
                    )

                    total_files += 1
                    total_size += file_size

                except Exception as e:
                    self.console.print(f"[red]Error reading {file_path}: {e}[/red]")

        self.codebase_files = files

        # Display summary
        self.console.print(f"[green]✓ Read {total_files} files ({total_size:,} bytes total)[/green]")
        self._display_codebase_structure()

        return files

    def _detect_language(self, file_path: Path) -> str:
        """Detect programming language from file extension."""
        ext = file_path.suffix.lower()
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.txt': 'text'
        }
        return language_map.get(ext, 'text')

    def _should_ignore_path(self, path: Path) -> bool:
        """Check if a path should be ignored."""
        ignore_patterns = {
            '__pycache__', '.git', '.venv', 'venv', 'node_modules',
            '.pytest_cache', 'dist', 'build', '.idea', '.vscode',
            '*.pyc', '*.pyo', '*.pyd', '.DS_Store', '*.log',
            '.env', '.gitignore', 'LICENSE', '*.lock'
        }

        path_str = str(path)
        path_name = path.name

        for pattern in ignore_patterns:
            if pattern in path_name or pattern in path_str:
                return True

        return False

    def _display_codebase_structure(self):
        """Display the codebase structure as a tree."""
        if not self.codebase_files:
            return

        tree = Tree("📁 Codebase Structure")

        # Group files by directory
        dirs = {}
        for file_path in self.codebase_files.keys():
            parts = Path(file_path).parts
            current = dirs

            for part in parts[:-1]:  # All but filename
                if part not in current:
                    current[part] = {}
                current = current[part]

            # Add file
            filename = parts[-1]
            file_info = self.codebase_files[file_path]
            current[filename] = f"{file_info.language} ({file_info.lines} lines)"

        def add_to_tree(node, data, level=0):
            if level > 3:  # Limit depth
                return

            for key, value in sorted(data.items()):
                if isinstance(value, dict):
                    branch = node.add(f"📁 {key}")
                    add_to_tree(branch, value, level + 1)
                else:
                    node.add(f"📄 {key} - {value}")

        add_to_tree(tree, dirs)
        self.console.print(tree)

    def understand_codebase(self, directory_path: Path) -> str:
        """Analyze and understand the codebase structure and purpose."""
        self.console.print("[bold blue]🧠 Understanding codebase...[/bold blue]")

        # Read the directory
        files = self.read_directory(directory_path)

        if not files:
            return "No readable files found in the directory."

        # Analyze the codebase with our existing analyzer
        self.analyzer.analyze_path(directory_path, recursive=True)

        # Create a comprehensive context
        context_parts = []

        # 1. File structure overview
        context_parts.append("=== CODEBASE STRUCTURE ===")
        for file_path, file_info in files.items():
            context_parts.append(f"File: {file_path} ({file_info.language}, {file_info.lines} lines)")

        # 2. Key files content (focus on important files)
        context_parts.append("\n=== KEY FILES CONTENT ===")
        important_files = self._identify_important_files(files)

        for file_path in important_files[:5]:  # Limit to top 5 important files
            file_info = files[file_path]
            context_parts.append(f"\n--- {file_path} ---")
            # Include first 50 lines or full content if shorter
            lines = file_info.content.splitlines()
            preview_lines = lines[:50]
            context_parts.append('\n'.join(preview_lines))
            if len(lines) > 50:
                context_parts.append(f"... ({len(lines) - 50} more lines)")

        # 3. Code analysis results
        db_stats = self.analyzer.vector_db.get_statistics()
        context_parts.append(f"\n=== ANALYSIS RESULTS ===")
        context_parts.append(f"Total code chunks: {db_stats['total_chunks']}")
        context_parts.append(f"Chunk types: {db_stats['chunk_types']}")

        full_context = '\n'.join(context_parts)

        # Use LLM to understand the codebase
        understanding_prompt = f"""Analyze this codebase and provide a comprehensive understanding:

{full_context}

Please provide:
1. What type of project this is (web app, library, tool, etc.)
2. Main technologies and frameworks used
3. Key components and their purposes
4. Architecture and structure
5. Entry points and main functionality
6. Dependencies and relationships between files

Be concise but thorough."""

        understanding = self.llm_manager.generate_code(understanding_prompt)

        self.console.print(Panel(understanding, title="🧠 Codebase Understanding"))
        return understanding

    def _identify_important_files(self, files: Dict[str, FileContent]) -> List[str]:
        """Identify the most important files in the codebase."""
        importance_scores = {}

        for file_path, file_info in files.items():
            score = 0
            filename = Path(file_path).name.lower()

            # Score based on filename importance
            if filename in ['main.py', 'app.py', 'index.js', 'server.py', '__init__.py']:
                score += 10
            elif filename.startswith('main') or filename.startswith('app'):
                score += 8
            elif filename in ['config.py', 'settings.py', 'requirements.txt', 'package.json']:
                score += 6
            elif filename.endswith('test.py') or 'test' in filename:
                score += 3

            # Score based on file size (larger files often more important)
            score += min(file_info.lines / 100, 5)

            # Score based on language
            if file_info.language in ['python', 'javascript', 'typescript', 'java']:
                score += 2

            importance_scores[file_path] = score

        # Return files sorted by importance
        return sorted(importance_scores.keys(), key=lambda x: importance_scores[x], reverse=True)

    def plan_modifications(self, request: str, directory_path: Path) -> ModificationPlan:
        """Create a detailed plan for modifying the codebase."""
        self.console.print(f"[bold blue]📋 Planning modifications for: {request}[/bold blue]")

        # Get codebase understanding (this also populates self.codebase_files)
        self.understand_codebase(directory_path)

        # Create a simple, robust planning approach
        files_summary = []
        for path, info in self.codebase_files.items():
            files_summary.append(f"- {path} ({info.language}, {info.lines} lines)")

        planning_prompt = f"""You are an expert software engineer. Analyze this codebase and create a plan for: {request}

CODEBASE FILES:
{chr(10).join(files_summary)}

MAIN FILE CONTENT (for context):
{self._get_main_file_content()}

REQUEST: {request}

Create a detailed plan. Respond with:

PLAN DESCRIPTION:
[Brief description of what will be implemented]

REASONING:
[Detailed explanation of the approach and why]

FILES TO MODIFY:
[List existing files that need changes, one per line]

FILES TO CREATE:
[List new files to create, one per line]

FILES TO DELETE:
[List files to delete, one per line]

MODIFICATIONS:
[For each file, describe what changes will be made]

Be specific and practical. Focus on the most important changes needed."""

        plan_response = self.llm_manager.generate_code(planning_prompt)

        # Parse the response using simple text parsing instead of JSON
        plan = self._parse_plan_response(plan_response, request)
        self._display_modification_plan(plan)
        return plan

    def _get_main_file_content(self) -> str:
        """Get content of the main file for context."""
        # Look for main files
        main_files = ['main.py', 'app.py', 'index.js', 'server.py']
        for main_file in main_files:
            if main_file in self.codebase_files:
                content = self.codebase_files[main_file].content
                # Return first 30 lines for context
                lines = content.splitlines()[:30]
                return '\n'.join(lines)

        # If no main file found, return content of first Python file
        for path, file_info in self.codebase_files.items():
            if file_info.language == 'python':
                lines = file_info.content.splitlines()[:30]
                return '\n'.join(lines)

        return "No main file found"

    def _parse_plan_response(self, response: str, request: str) -> ModificationPlan:
        """Parse the plan response using simple text parsing."""
        lines = response.split('\n')

        description = f"Implement: {request}"
        reasoning = "AI-generated plan"
        files_to_modify = []
        files_to_create = []
        files_to_delete = []
        modifications = []

        current_section = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Detect sections
            if line.startswith('PLAN DESCRIPTION:'):
                current_section = 'description'
                continue
            elif line.startswith('REASONING:'):
                current_section = 'reasoning'
                continue
            elif line.startswith('FILES TO MODIFY:'):
                current_section = 'modify'
                continue
            elif line.startswith('FILES TO CREATE:'):
                current_section = 'create'
                continue
            elif line.startswith('FILES TO DELETE:'):
                current_section = 'delete'
                continue
            elif line.startswith('MODIFICATIONS:'):
                current_section = 'modifications'
                continue

            # Parse content based on current section
            if current_section == 'description' and line:
                description = line
            elif current_section == 'reasoning' and line:
                reasoning = line
            elif current_section == 'modify' and line.startswith('-'):
                file_name = line.replace('-', '').strip()
                if file_name:
                    files_to_modify.append(file_name)
                    modifications.append(CodeChange(
                        file_path=file_name,
                        action="modify",
                        content="",
                        description=f"Modify {file_name} for: {request}"
                    ))
            elif current_section == 'create' and line.startswith('-'):
                file_name = line.replace('-', '').strip()
                if file_name:
                    files_to_create.append(file_name)
                    modifications.append(CodeChange(
                        file_path=file_name,
                        action="create",
                        content="",
                        description=f"Create {file_name} for: {request}"
                    ))
            elif current_section == 'delete' and line.startswith('-'):
                file_name = line.replace('-', '').strip()
                if file_name:
                    files_to_delete.append(file_name)

        # If no specific plan was parsed, create a smart default
        if not modifications:
            # Determine the best approach based on the request
            if any(keyword in request.lower() for keyword in ['add', 'create', 'new']):
                # For adding new functionality, prefer modifying existing files
                if 'main.py' in self.codebase_files:
                    modifications.append(CodeChange(
                        file_path="main.py",
                        action="modify",
                        content="",
                        description=f"Add functionality to main.py: {request}"
                    ))
                    files_to_modify.append("main.py")
                else:
                    # Create new file
                    new_file = self._suggest_filename(request)
                    modifications.append(CodeChange(
                        file_path=new_file,
                        action="create",
                        content="",
                        description=f"Create {new_file} for: {request}"
                    ))
                    files_to_create.append(new_file)
            else:
                # For other requests, create a new feature file
                new_file = self._suggest_filename(request)
                modifications.append(CodeChange(
                    file_path=new_file,
                    action="create",
                    content="",
                    description=f"Create {new_file} for: {request}"
                ))
                files_to_create.append(new_file)

        return ModificationPlan(
            description=description,
            files_to_modify=files_to_modify,
            files_to_create=files_to_create,
            files_to_delete=files_to_delete,
            modifications=modifications,
            reasoning=reasoning
        )

    def _suggest_filename(self, request: str) -> str:
        """Suggest an appropriate filename based on the request."""
        request_lower = request.lower()

        if 'test' in request_lower:
            return 'test_functions.py'
        elif 'util' in request_lower or 'helper' in request_lower:
            return 'utils.py'
        elif 'config' in request_lower or 'setting' in request_lower:
            return 'config.py'
        elif 'auth' in request_lower or 'login' in request_lower:
            return 'auth.py'
        elif 'api' in request_lower or 'endpoint' in request_lower:
            return 'api.py'
        elif 'database' in request_lower or 'db' in request_lower:
            return 'database.py'
        elif 'model' in request_lower:
            return 'models.py'
        elif 'view' in request_lower or 'ui' in request_lower:
            return 'views.py'
        elif 'calculator' in request_lower or 'math' in request_lower:
            return 'calculator.py'
        else:
            return 'new_feature.py'

    def _display_modification_plan(self, plan: ModificationPlan):
        """Display the modification plan in a nice format."""
        self.console.print(Panel(plan.description, title="📋 Modification Plan"))

        if plan.reasoning:
            self.console.print(Panel(plan.reasoning, title="🤔 Reasoning"))

        # Create summary table
        table = Table(title="📊 Planned Changes")
        table.add_column("Action", style="cyan")
        table.add_column("Files", style="green")
        table.add_column("Count", style="yellow")

        table.add_row("Modify", ", ".join(plan.files_to_modify), str(len(plan.files_to_modify)))
        table.add_row("Create", ", ".join(plan.files_to_create), str(len(plan.files_to_create)))
        table.add_row("Delete", ", ".join(plan.files_to_delete), str(len(plan.files_to_delete)))

        self.console.print(table)

        # Show detailed modifications
        if plan.modifications:
            mod_table = Table(title="🔧 Detailed Modifications")
            mod_table.add_column("File", style="cyan")
            mod_table.add_column("Action", style="green")
            mod_table.add_column("Description", style="dim")

            for mod in plan.modifications:
                mod_table.add_row(mod.file_path, mod.action, mod.description)

            self.console.print(mod_table)

    def execute_modifications(self, plan: ModificationPlan, directory_path: Path, dry_run: bool = False) -> Dict[str, Any]:
        """Execute the planned modifications on the codebase."""
        if dry_run:
            self.console.print("[bold yellow]🔍 DRY RUN - No files will be modified[/bold yellow]")
        else:
            self.console.print("[bold green]⚡ Executing modifications...[/bold green]")

        results = {
            'success': True,
            'modified_files': [],
            'created_files': [],
            'deleted_files': [],
            'errors': []
        }

        try:
            # 1. Delete files first
            for file_path in plan.files_to_delete:
                full_path = directory_path / file_path
                if full_path.exists():
                    if not dry_run:
                        full_path.unlink()
                    results['deleted_files'].append(file_path)
                    self.console.print(f"[red]🗑️  Deleted: {file_path}[/red]")

            # 2. Process modifications and creations
            for modification in plan.modifications:
                try:
                    if modification.action == "create":
                        self._create_file(modification, directory_path, dry_run, results)
                    elif modification.action == "modify":
                        self._modify_file(modification, directory_path, dry_run, results)

                except Exception as e:
                    error_msg = f"Error processing {modification.file_path}: {e}"
                    results['errors'].append(error_msg)
                    self.console.print(f"[red]❌ {error_msg}[/red]")
                    results['success'] = False

            # 3. Display summary
            self._display_execution_summary(results, dry_run)

        except Exception as e:
            results['success'] = False
            results['errors'].append(f"Execution failed: {e}")
            self.console.print(f"[red]❌ Execution failed: {e}[/red]")

        return results

    def _create_file(self, modification: CodeChange, directory_path: Path, dry_run: bool, results: Dict):
        """Create a new file with AI-generated content."""
        file_path = modification.file_path
        full_path = directory_path / file_path

        # Generate content for the new file
        if not modification.content:
            content = self._generate_file_content(modification, directory_path)
        else:
            content = modification.content

        if not dry_run:
            # Create directory if it doesn't exist
            full_path.parent.mkdir(parents=True, exist_ok=True)

            # Write the file
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)

        results['created_files'].append(file_path)
        self.console.print(f"[green]✨ Created: {file_path}[/green]")

        # Show preview of created content
        if content:
            preview = content[:200] + "..." if len(content) > 200 else content
            syntax = Syntax(preview, self._detect_language(Path(file_path)), theme="monokai")
            self.console.print(Panel(syntax, title=f"📄 Preview: {file_path}"))

    def _modify_file(self, modification: CodeChange, directory_path: Path, dry_run: bool, results: Dict):
        """Modify an existing file with AI assistance."""
        file_path = modification.file_path
        full_path = directory_path / file_path

        if not full_path.exists():
            self.console.print(f"[yellow]⚠️  File not found, creating instead: {file_path}[/yellow]")
            self._create_file(modification, directory_path, dry_run, results)
            return

        # Read current content
        with open(full_path, 'r', encoding='utf-8') as f:
            current_content = f.read()

        # Generate modified content
        modified_content = self._generate_modified_content(
            current_content, modification, file_path
        )

        if not dry_run and modified_content != current_content:
            # Backup original file
            backup_path = full_path.with_suffix(full_path.suffix + '.backup')
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(current_content)

            # Write modified content
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(modified_content)

            self.console.print(f"[blue]📝 Modified: {file_path} (backup: {backup_path.name})[/blue]")

        results['modified_files'].append(file_path)

        # Show diff preview
        self._show_modification_preview(current_content, modified_content, file_path)

    def _generate_file_content(self, modification: CodeChange, directory_path: Path) -> str:
        """Generate content for a new file using AI."""
        # Get context about the codebase
        context_files = []
        for path, file_info in self.codebase_files.items():
            if file_info.language == self._detect_language(Path(modification.file_path)):
                context_files.append(f"=== {path} ===\n{file_info.content[:500]}...")

        context = "\n\n".join(context_files[:3])  # Limit context

        prompt = f"""Create a new file: {modification.file_path}

Purpose: {modification.description}

Codebase context (similar files):
{context}

Generate complete, production-ready code that:
1. Follows the existing codebase patterns and style
2. Includes proper documentation and comments
3. Has appropriate error handling
4. Integrates well with the existing code
5. Follows best practices for {self._detect_language(Path(modification.file_path))}

Generate only the file content, no explanations:"""

        return self.llm_manager.generate_code(prompt)

    def _generate_modified_content(self, current_content: str, modification: CodeChange, file_path: str) -> str:
        """Generate modified content for an existing file."""
        prompt = f"""Modify this file: {file_path}

Current content:
```
{current_content}
```

Required changes: {modification.description}

Generate the complete modified file content that:
1. Implements the requested changes
2. Preserves existing functionality that shouldn't change
3. Maintains the same code style and patterns
4. Includes proper error handling
5. Updates documentation/comments as needed

Generate only the complete modified file content, no explanations:"""

        return self.llm_manager.generate_code(prompt)

    def _show_modification_preview(self, original: str, modified: str, file_path: str):
        """Show a preview of the modifications made."""
        if original == modified:
            self.console.print(f"[dim]No changes made to {file_path}[/dim]")
            return

        # Simple diff preview (first 5 lines that changed)
        orig_lines = original.splitlines()
        mod_lines = modified.splitlines()

        changes_shown = 0
        for i, (orig_line, mod_line) in enumerate(zip(orig_lines, mod_lines)):
            if orig_line != mod_line and changes_shown < 3:
                self.console.print(f"[red]- {orig_line}[/red]")
                self.console.print(f"[green]+ {mod_line}[/green]")
                changes_shown += 1

        if len(mod_lines) != len(orig_lines):
            self.console.print(f"[blue]Lines changed: {len(orig_lines)} → {len(mod_lines)}[/blue]")

    def _display_execution_summary(self, results: Dict, dry_run: bool):
        """Display a summary of the execution results."""
        title = "🔍 Dry Run Summary" if dry_run else "✅ Execution Summary"

        summary_table = Table(title=title)
        summary_table.add_column("Action", style="cyan")
        summary_table.add_column("Count", style="green")
        summary_table.add_column("Files", style="dim")

        summary_table.add_row(
            "Created",
            str(len(results['created_files'])),
            ", ".join(results['created_files'][:3]) + ("..." if len(results['created_files']) > 3 else "")
        )
        summary_table.add_row(
            "Modified",
            str(len(results['modified_files'])),
            ", ".join(results['modified_files'][:3]) + ("..." if len(results['modified_files']) > 3 else "")
        )
        summary_table.add_row(
            "Deleted",
            str(len(results['deleted_files'])),
            ", ".join(results['deleted_files'][:3]) + ("..." if len(results['deleted_files']) > 3 else "")
        )

        if results['errors']:
            summary_table.add_row("Errors", str(len(results['errors'])), "❌ See above")

        self.console.print(summary_table)

        if results['success'] and not dry_run:
            self.console.print("[bold green]🎉 All modifications completed successfully![/bold green]")
        elif dry_run:
            self.console.print("[bold blue]🔍 Dry run completed. Use --execute to apply changes.[/bold blue]")

    def agentic_code_session(self, directory_path: Path, request: str, execute: bool = False) -> Dict[str, Any]:
        """Complete agentic coding session: understand, plan, and execute."""
        self.console.print(Panel(
            f"🤖 Starting agentic coding session\n\n"
            f"📁 Directory: {directory_path}\n"
            f"🎯 Request: {request}\n"
            f"⚡ Execute: {'Yes' if execute else 'No (dry run)'}",
            title="🚀 Agentic Coder"
        ))

        try:
            # Step 1: Understand the codebase
            understanding = self.understand_codebase(directory_path)

            # Step 2: Plan modifications
            plan = self.plan_modifications(request, directory_path)

            # Step 3: Execute (or dry run)
            results = self.execute_modifications(plan, directory_path, dry_run=not execute)

            return {
                'success': True,
                'understanding': understanding,
                'plan': plan,
                'execution_results': results
            }

        except Exception as e:
            self.console.print(f"[red]❌ Agentic coding session failed: {e}[/red]")
            return {
                'success': False,
                'error': str(e)
            }
