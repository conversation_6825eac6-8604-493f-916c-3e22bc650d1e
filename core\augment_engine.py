"""
Augment-style iterative coding engine that writes, executes, reads terminal, and self-corrects.
"""

import os
import time
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.progress import Progress, SpinnerColumn, TextColumn

from llm_integration import <PERSON>MManager
from code_executor import SafeCodeExecutor, ExecutionResult
from analyzer import CodebaseAnalyzer


@dataclass
class IterationResult:
    """Result of a single iteration in the coding loop."""
    iteration: int
    code_generated: str
    execution_result: ExecutionResult
    terminal_output: str
    error_analysis: str
    correction_needed: bool
    success: bool


@dataclass
class AugmentSession:
    """Complete Augment-style coding session with iterations."""
    prompt: str
    codebase_path: Path
    iterations: List[IterationResult]
    final_success: bool
    total_iterations: int
    final_code: str
    execution_time: float


class AugmentEngine:
    """Augment-style iterative coding engine with self-correction."""

    def __init__(self):
        self.console = Console()
        self.llm_manager = LLMManager()
        self.executor = SafeCodeExecutor()
        self.analyzer = CodebaseAnalyzer()
        self.max_iterations = 5
        self.current_session: Optional[AugmentSession] = None

    def augment_code(self, prompt: str, codebase_path: Path, execute_in_terminal: bool = True) -> AugmentSession:
        """
        Main Augment-style coding function:
        1. Understand codebase
        2. Generate code
        3. Execute and read terminal
        4. If errors, analyze and correct
        5. Repeat until success or max iterations
        """
        start_time = time.time()

        self.console.print(Panel(
            f"🤖 **AUGMENT ENGINE ACTIVATED**\n\n"
            f"📁 Codebase: {codebase_path}\n"
            f"🎯 Prompt: {prompt}\n"
            f"🔄 Max Iterations: {self.max_iterations}\n"
            f"⚡ Terminal Execution: {'Yes' if execute_in_terminal else 'No'}",
            title="🚀 Augment Coding Session",
            border_style="bold blue"
        ))

        # Step 1: Understand the codebase
        codebase_context = self._understand_codebase(codebase_path)

        # Initialize session
        session = AugmentSession(
            prompt=prompt,
            codebase_path=codebase_path,
            iterations=[],
            final_success=False,
            total_iterations=0,
            final_code="",
            execution_time=0.0
        )
        self.current_session = session

        # Step 2: Iterative coding loop
        current_code = ""
        error_context = ""

        for iteration in range(1, self.max_iterations + 1):
            self.console.print(f"\n[bold cyan]🔄 ITERATION {iteration}[/bold cyan]")

            # Generate/correct code
            if iteration == 1:
                code = self._generate_initial_code(prompt, codebase_context)
            else:
                code = self._correct_code(current_code, error_context, prompt, codebase_context)

            # Execute the code
            if execute_in_terminal:
                execution_result, terminal_output = self._execute_in_terminal(code, codebase_path)
            else:
                execution_result = self.executor.execute_code(code)
                terminal_output = execution_result.output

            # Analyze results
            success = execution_result.success and not self._has_runtime_errors(terminal_output)

            if not success:
                error_analysis = self._analyze_errors(code, execution_result, terminal_output)
                correction_needed = True
            else:
                error_analysis = "Code executed successfully!"
                correction_needed = False

            # Record iteration
            iteration_result = IterationResult(
                iteration=iteration,
                code_generated=code,
                execution_result=execution_result,
                terminal_output=terminal_output,
                error_analysis=error_analysis,
                correction_needed=correction_needed,
                success=success
            )
            session.iterations.append(iteration_result)

            # Display iteration results
            self._display_iteration_result(iteration_result)

            if success:
                self.console.print(f"[bold green]✅ SUCCESS in iteration {iteration}![/bold green]")
                session.final_success = True
                session.final_code = code
                break
            else:
                current_code = code
                error_context = f"Previous attempt failed with: {error_analysis}"
                self.console.print(f"[yellow]⚠️  Iteration {iteration} failed, trying again...[/yellow]")

        # Finalize session
        session.total_iterations = len(session.iterations)
        session.execution_time = time.time() - start_time

        if not session.final_success:
            self.console.print(f"[red]❌ Failed to solve after {self.max_iterations} iterations[/red]")
            session.final_code = current_code  # Use last attempt

        self._display_session_summary(session)
        return session

    def _understand_codebase(self, codebase_path: Path) -> str:
        """Understand the codebase structure and context."""
        self.console.print("[bold blue]🧠 Understanding codebase...[/bold blue]")

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task("Analyzing codebase...", total=None)

            # Analyze the codebase
            analysis_result = self.analyzer.analyze_path(codebase_path, recursive=True)

            # Get file structure
            files_info = []
            for root, dirs, files in os.walk(codebase_path):
                for file in files:
                    if file.endswith(('.py', '.js', '.ts', '.java', '.cpp', '.c')):
                        rel_path = os.path.relpath(os.path.join(root, file), codebase_path)
                        files_info.append(rel_path)

            progress.update(task, description="Building context...")

            # Create comprehensive context
            context = f"""
CODEBASE ANALYSIS:
- Files processed: {analysis_result['files_processed']}
- Code chunks: {analysis_result['chunks_extracted']}
- Embeddings: {analysis_result['embeddings_generated']}

KEY FILES:
{chr(10).join(f"- {file}" for file in files_info[:10])}

STRUCTURE: {len(files_info)} total code files
"""

        self.console.print("[green]✓ Codebase understanding complete[/green]")
        return context

    def _generate_initial_code(self, prompt: str, codebase_context: str) -> str:
        """Generate initial code based on prompt and codebase context."""
        self.console.print("[bold yellow]🎨 Generating initial code...[/bold yellow]")

        generation_prompt = f"""You are an expert programmer working with an existing codebase.

CODEBASE CONTEXT:
{codebase_context}

USER REQUEST: {prompt}

Generate complete, executable Python code that:
1. Integrates well with the existing codebase
2. Follows the project's patterns and style
3. Includes proper error handling
4. Has clear documentation
5. Can be executed immediately

IMPORTANT: Generate ONLY the Python code, no explanations or markdown formatting.
The code should be ready to save to a file and execute."""

        code = self.llm_manager.generate_code(generation_prompt)

        # Clean up the code (remove markdown if present)
        if code.startswith('```python'):
            code = code[9:]
        if code.endswith('```'):
            code = code[:-3]

        return code.strip()

    def _execute_in_terminal(self, code: str, codebase_path: Path) -> Tuple[ExecutionResult, str]:
        """Execute code in terminal and capture all output."""
        self.console.print("[bold green]⚡ Executing in terminal...[/bold green]")

        # Create temporary file
        temp_file = codebase_path / "temp_augment_code.py"

        try:
            # Ensure directory exists
            codebase_path.mkdir(parents=True, exist_ok=True)

            # Write code to file
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(code)

            # Verify file was created
            if not temp_file.exists():
                raise FileNotFoundError(f"Failed to create temp file: {temp_file}")

            # Execute in terminal
            result = subprocess.run(
                ['python', str(temp_file.absolute())],
                cwd=str(codebase_path.absolute()),
                capture_output=True,
                text=True,
                timeout=30
            )

            # Create execution result
            execution_result = ExecutionResult(
                success=result.returncode == 0,
                output=result.stdout,
                error=result.stderr,
                execution_time=0.0
            )

            terminal_output = f"STDOUT:\n{result.stdout}\n\nSTDERR:\n{result.stderr}\n\nReturn Code: {result.returncode}"

            return execution_result, terminal_output

        except subprocess.TimeoutExpired:
            execution_result = ExecutionResult(
                success=False,
                output="",
                error="Execution timed out after 30 seconds",
                execution_time=30.0
            )
            return execution_result, "TIMEOUT: Code execution exceeded 30 seconds"

        except Exception as e:
            execution_result = ExecutionResult(
                success=False,
                output="",
                error=str(e),
                execution_time=0.0
            )
            return execution_result, f"EXECUTION ERROR: {e}"

        finally:
            # Clean up temp file
            if temp_file.exists():
                temp_file.unlink()

    def _has_runtime_errors(self, terminal_output: str) -> bool:
        """Check if terminal output indicates runtime errors."""
        error_indicators = [
            'Traceback (most recent call last)',
            'Error:', 'Exception:', 'SyntaxError:', 'NameError:',
            'TypeError:', 'ValueError:', 'ImportError:', 'ModuleNotFoundError:'
        ]

        return any(indicator in terminal_output for indicator in error_indicators)

    def _analyze_errors(self, code: str, execution_result: ExecutionResult, terminal_output: str) -> str:
        """Analyze errors and provide detailed analysis."""
        self.console.print("[bold red]🔍 Analyzing errors...[/bold red]")

        # Check for common issues first
        if "can't open file" in terminal_output or "No such file or directory" in terminal_output:
            return """FILE CREATION ERROR: The temporary file could not be created or found.
This is likely a system/path issue, not a code issue.
The code itself appears to be syntactically correct.
Try removing any problematic imports or file references."""

        if "ModuleNotFoundError" in terminal_output or "ImportError" in terminal_output:
            # Extract the specific module that failed
            import_error_module = "unknown"
            if "No module named" in terminal_output:
                start = terminal_output.find("No module named '") + 17
                end = terminal_output.find("'", start)
                if start > 16 and end > start:
                    import_error_module = terminal_output[start:end]

            return f"""IMPORT ERROR: The code is trying to import '{import_error_module}' which is not available.
SOLUTION: Rewrite the code to use only Python standard library modules.
- Replace pandas with built-in csv module
- Replace external libraries with standard library equivalents
- Implement functionality from scratch using basic Python
- Use io.StringIO instead of pandas.compat.StringIO
- Create simple data structures instead of DataFrames"""

        if "pd.compat.StringIO" in terminal_output or "pandas" in terminal_output:
            return """PANDAS ERROR: The code is using pandas which is not available.
SOLUTION: Rewrite to use Python standard library:
- Use csv.reader() instead of pd.read_csv()
- Use io.StringIO() instead of pd.compat.StringIO()
- Use lists and dictionaries instead of DataFrames
- Implement statistics calculations manually or use statistics module"""

        if "NotImplementedError" in terminal_output:
            return """LOGIC ERROR: The code is calling a base class method that raises NotImplementedError.
SOLUTION: Use the specific algorithm classes instead of the base class.
- Replace SortingAlgorithm(data).sort() with algorithm(data).sort()
- Make sure to instantiate the correct algorithm class for each test"""

        analysis_prompt = f"""Analyze this code execution failure and provide specific guidance for fixing it:

CODE THAT FAILED:
```python
{code}
```

EXECUTION RESULT:
- Success: {execution_result.success}
- Error: {execution_result.error}

TERMINAL OUTPUT:
{terminal_output}

Provide a concise analysis of:
1. What exactly went wrong
2. The root cause of the error
3. Specific steps to fix it
4. Any missing imports or dependencies

Be specific and actionable."""

        return self.llm_manager.generate_code(analysis_prompt)

    def _correct_code(self, previous_code: str, error_context: str, original_prompt: str, codebase_context: str) -> str:
        """Generate corrected code based on previous errors."""
        self.console.print("[bold yellow]🔧 Correcting code based on errors...[/bold yellow]")

        # Apply specific fixes based on error type
        if "IMPORT ERROR" in error_context or "ModuleNotFoundError" in error_context:
            return self._fix_import_errors(previous_code, original_prompt)
        elif "PANDAS ERROR" in error_context:
            return self._fix_pandas_errors(previous_code, original_prompt)
        else:
            # General correction
            return self._general_correction(previous_code, error_context, original_prompt, codebase_context)

    def _fix_import_errors(self, previous_code: str, original_prompt: str) -> str:
        """Fix import errors by replacing with standard library equivalents."""
        self.console.print("[dim]Applying import error fixes...[/dim]")

        # Remove problematic imports and replace with standard library
        lines = previous_code.split('\n')
        fixed_lines = []

        for line in lines:
            # Skip problematic imports
            if any(bad_import in line for bad_import in ['nltk', 'pandas', 'numpy', 'sklearn']):
                if 'import' in line:
                    continue  # Skip the entire import line

            # Replace specific function calls
            line = line.replace('word_tokenize(', 'self._simple_tokenize(')
            line = line.replace('SentimentIntensityAnalyzer()', 'self._simple_sentiment_analyzer()')

            fixed_lines.append(line)

        # Add simple implementations
        if 'TextAnalyzer' in previous_code:
            # Add simple tokenization method
            tokenize_method = '''
    def _simple_tokenize(self, text):
        """Simple word tokenization using regex."""
        import re
        words = re.findall(r'\\b\\w+\\b', text.lower())
        return words

    def _simple_sentiment_analyzer(self):
        """Simple sentiment analysis using keyword matching."""
        positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'joy']
        negative_words = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'sad', 'angry', 'horrible', 'disgusting']

        words = self._simple_tokenize(self.text)
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)

        if positive_count + negative_count == 0:
            return 0.0

        return (positive_count - negative_count) / (positive_count + negative_count)'''

            # Insert the methods into the TextAnalyzer class
            class_start = -1
            for i, line in enumerate(fixed_lines):
                if 'class TextAnalyzer:' in line:
                    class_start = i
                elif class_start != -1 and line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                    # Found end of class, insert methods before
                    fixed_lines.insert(i, tokenize_method)
                    break

            if class_start != -1 and class_start == len(fixed_lines) - 1:
                # Class is at the end, append methods
                fixed_lines.append(tokenize_method)

        return '\n'.join(fixed_lines)

    def _fix_pandas_errors(self, previous_code: str, original_prompt: str) -> str:
        """Fix pandas-related errors by using standard library."""
        self.console.print("[dim]Applying pandas error fixes...[/dim]")

        # Replace pandas with csv module
        fixed_code = previous_code.replace('import pandas as pd', 'import csv\nimport io')
        fixed_code = fixed_code.replace('pd.read_csv(pd.compat.StringIO(', 'list(csv.reader(io.StringIO(')
        fixed_code = fixed_code.replace('pd.read_csv(', 'list(csv.reader(')

        return fixed_code

    def _general_correction(self, previous_code: str, error_context: str, original_prompt: str, codebase_context: str) -> str:
        """General correction using LLM."""
        correction_prompt = f"""Fix the following code that failed to execute properly:

ORIGINAL REQUEST: {original_prompt}

PREVIOUS CODE THAT FAILED:
```python
{previous_code}
```

ERROR ANALYSIS:
{error_context}

CRITICAL REQUIREMENTS:
1. Use ONLY Python standard library modules (no external dependencies)
2. Replace any external libraries with built-in equivalents
3. Implement functionality from scratch if needed
4. Ensure the code will execute without any import errors

Generate the corrected Python code that will execute successfully:"""

        corrected_code = self.llm_manager.generate_code(correction_prompt)

        # Clean up the code
        if corrected_code.startswith('```python'):
            corrected_code = corrected_code[9:]
        if corrected_code.endswith('```'):
            corrected_code = corrected_code[:-3]

        return corrected_code.strip()

    def _display_iteration_result(self, result: IterationResult):
        """Display the results of a single iteration."""
        status_color = "green" if result.success else "red"
        status_icon = "✅" if result.success else "❌"

        self.console.print(f"\n[{status_color}]{status_icon} Iteration {result.iteration} Result[/{status_color}]")

        # Show code preview
        code_preview = result.code_generated[:200] + "..." if len(result.code_generated) > 200 else result.code_generated
        syntax = Syntax(code_preview, "python", theme="monokai", line_numbers=True)
        self.console.print(Panel(syntax, title="Generated Code"))

        # Show execution output
        if result.terminal_output:
            output_preview = result.terminal_output[:300] + "..." if len(result.terminal_output) > 300 else result.terminal_output
            self.console.print(Panel(output_preview, title="Terminal Output"))

        # Show error analysis if needed
        if not result.success:
            self.console.print(Panel(result.error_analysis, title="Error Analysis", border_style="red"))

    def _display_session_summary(self, session: AugmentSession):
        """Display final session summary."""
        title = "🎉 SESSION COMPLETED" if session.final_success else "⚠️ SESSION INCOMPLETE"
        color = "green" if session.final_success else "yellow"

        summary = f"""
[bold {color}]{title}[/bold {color}]

📊 **Session Statistics:**
• Total Iterations: {session.total_iterations}
• Final Success: {'✅ Yes' if session.final_success else '❌ No'}
• Execution Time: {session.execution_time:.2f} seconds
• Final Code Length: {len(session.final_code)} characters

🎯 **Original Prompt:** {session.prompt}

📁 **Codebase:** {session.codebase_path}
"""

        self.console.print(Panel(summary, border_style=color))

        if session.final_success and session.final_code:
            self.console.print("\n[bold green]🏆 FINAL WORKING CODE:[/bold green]")
            syntax = Syntax(session.final_code, "python", theme="monokai", line_numbers=True)
            self.console.print(Panel(syntax, title="Final Code"))

    def save_final_code(self, session: AugmentSession, filename: str = None) -> Path:
        """Save the final working code to a file."""
        if not session.final_success:
            raise ValueError("Cannot save code from unsuccessful session")

        if not filename:
            # Generate filename based on prompt
            safe_prompt = "".join(c for c in session.prompt[:30] if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"augment_{safe_prompt.replace(' ', '_').lower()}.py"

        file_path = session.codebase_path / filename

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(session.final_code)

        self.console.print(f"[green]💾 Final code saved to: {file_path}[/green]")
        return file_path

    def get_session_report(self, session: AugmentSession) -> str:
        """Generate a detailed report of the coding session."""
        report = f"""
# Augment Coding Session Report

## Session Overview
- **Prompt**: {session.prompt}
- **Codebase**: {session.codebase_path}
- **Success**: {'✅ Yes' if session.final_success else '❌ No'}
- **Total Iterations**: {session.total_iterations}
- **Execution Time**: {session.execution_time:.2f} seconds

## Iteration Details
"""

        for iteration in session.iterations:
            report += f"""
### Iteration {iteration.iteration}
- **Success**: {'✅' if iteration.success else '❌'}
- **Code Length**: {len(iteration.code_generated)} characters
- **Error Analysis**: {iteration.error_analysis[:100]}...
"""

        if session.final_success:
            report += f"""
## Final Working Code
```python
{session.final_code}
```
"""

        return report
