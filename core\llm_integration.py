"""LLM integration for code generation and modification."""

import os
import requests
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path
import core.config as config


@dataclass
class CodeChange:
    """Represents a code change to be made."""
    file_path: str
    action: str  # 'create', 'modify', 'delete'
    content: str
    start_line: Optional[int] = None
    end_line: Optional[int] = None
    description: str = ""


class LLMProvider:
    """Base class for LLM providers."""

    def generate_code(self, prompt: str, context: str = "") -> str:
        """Generate code based on prompt and context."""
        raise NotImplementedError

    def analyze_code(self, code: str, question: str) -> str:
        """Analyze code and answer questions about it."""
        raise NotImplementedError


class OpenAIProvider(LLMProvider):
    """OpenAI GPT integration."""

    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.base_url = "https://api.openai.com/v1"

    def generate_code(self, prompt: str, context: str = "") -> str:
        """Generate code using OpenAI GPT."""
        if not self.api_key:
            return self._fallback_response(prompt)

        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            system_prompt = """You are an expert programmer. Generate COMPLETE, WORKING code that runs immediately without any placeholders, undefined functions, or missing imports.

CRITICAL REQUIREMENTS:
- Write COMPLETE, SELF-CONTAINED code that runs immediately
- NO placeholders like "load_data()" or "undefined_function()"
- NO missing imports - include ALL necessary imports at the top
- NO TODO comments or incomplete functions
- DEFINE ALL functions and classes that are used
- Include ALL data generation, processing, and output in one file
- The code must execute successfully from start to finish
- If you need data, GENERATE it in the code (don't load from external files)
- If you need libraries, use only standard library or generate alternatives
- Include proper error handling and docstrings
- Add a main section that demonstrates the functionality

Return ONLY the complete code that runs without errors, no explanations."""

            if context:
                system_prompt += f"\n\nContext about the codebase:\n{context}"

            data = {
                'model': 'gpt-3.5-turbo',
                'messages': [
                    {'role': 'system', 'content': system_prompt},
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': 2000,
                'temperature': 0.1
            }

            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            else:
                print(f"OpenAI API error: {response.status_code}")
                return self._fallback_response(prompt)

        except Exception as e:
            print(f"Error calling OpenAI API: {e}")
            return self._fallback_response(prompt)

    def analyze_code(self, code: str, question: str) -> str:
        """Analyze code using OpenAI GPT."""
        if not self.api_key:
            return "OpenAI API key not configured. Please set OPENAI_API_KEY environment variable."

        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            prompt = f"""Analyze this code and answer the question:

Code:
```
{code}
```

Question: {question}

Provide a clear, concise answer."""

            data = {
                'model': 'gpt-3.5-turbo',
                'messages': [
                    {'role': 'system', 'content': 'You are an expert code analyst. Provide clear, accurate analysis of code.'},
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': 1000,
                'temperature': 0.1
            }

            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            else:
                return f"API error: {response.status_code}"

        except Exception as e:
            return f"Error analyzing code: {e}"

    def _fallback_response(self, prompt: str) -> str:
        """Fallback response when API is not available."""
        # Generate actual code based on common patterns
        if "factorial" in prompt.lower():
            return """def factorial(n):
    \"\"\"Calculate the factorial of a number.\"\"\"
    if n < 0:
        raise ValueError("Factorial is not defined for negative numbers")
    if n == 0 or n == 1:
        return 1
    return n * factorial(n - 1)

# Example usage
if __name__ == "__main__":
    print(f"Factorial of 5: {factorial(5)}")
"""
        elif "fibonacci" in prompt.lower():
            return """def fibonacci(n):
    \"\"\"Generate the nth Fibonacci number.\"\"\"
    if n < 0:
        raise ValueError("Fibonacci is not defined for negative numbers")
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

def fibonacci_sequence(count):
    \"\"\"Generate a sequence of Fibonacci numbers.\"\"\"
    return [fibonacci(i) for i in range(count)]

# Example usage
if __name__ == "__main__":
    print(f"Fibonacci(10): {fibonacci(10)}")
    print(f"First 10 Fibonacci numbers: {fibonacci_sequence(10)}")
"""
        elif "sort" in prompt.lower():
            return """def bubble_sort(arr):
    \"\"\"Sort an array using bubble sort algorithm.\"\"\"
    n = len(arr)
    for i in range(n):
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
    return arr

def quick_sort(arr):
    \"\"\"Sort an array using quick sort algorithm.\"\"\"
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quick_sort(left) + middle + quick_sort(right)

# Example usage
if __name__ == "__main__":
    test_array = [64, 34, 25, 12, 22, 11, 90]
    print(f"Original: {test_array}")
    print(f"Bubble sorted: {bubble_sort(test_array.copy())}")
    print(f"Quick sorted: {quick_sort(test_array.copy())}")
"""
        elif "calculator" in prompt.lower() or "math" in prompt.lower():
            return """class Calculator:
    \"\"\"A simple calculator class with basic operations.\"\"\"

    def add(self, a, b):
        \"\"\"Add two numbers.\"\"\"
        return a + b

    def subtract(self, a, b):
        \"\"\"Subtract b from a.\"\"\"
        return a - b

    def multiply(self, a, b):
        \"\"\"Multiply two numbers.\"\"\"
        return a * b

    def divide(self, a, b):
        \"\"\"Divide a by b.\"\"\"
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return a / b

    def power(self, base, exponent):
        \"\"\"Calculate base raised to the power of exponent.\"\"\"
        return base ** exponent

# Example usage
if __name__ == "__main__":
    calc = Calculator()
    print(f"5 + 3 = {calc.add(5, 3)}")
    print(f"10 - 4 = {calc.subtract(10, 4)}")
    print(f"6 * 7 = {calc.multiply(6, 7)}")
    print(f"15 / 3 = {calc.divide(15, 3)}")
    print(f"2^8 = {calc.power(2, 8)}")
"""
        else:
            # Generic function template
            function_name = prompt.lower().replace(" ", "_").replace("create", "").replace("function", "").replace("to", "").strip()
            if not function_name:
                function_name = "custom_function"

            return f"""def {function_name}():
    \"\"\"
    {prompt}

    This function was generated based on your request.
    Please modify it according to your specific needs.
    \"\"\"
    # TODO: Implement the logic for: {prompt}
    pass

# Example usage
if __name__ == "__main__":
    result = {function_name}()
    print(f"Result: {{result}}")
"""


class OllamaProvider(LLMProvider):
    """Local Ollama integration for privacy-focused users."""

    def __init__(self, base_url: str = "http://localhost:11434", model: str = "codellama"):
        self.base_url = base_url
        self.model = model

    def generate_code(self, prompt: str, context: str = "") -> str:
        """Generate code using local Ollama."""
        try:
            # CodeLlama works better with specific formatting
            full_prompt = f"""<s>[INST] You are an expert Python programmer. Generate COMPLETE, WORKING Python code that runs immediately without any placeholders, undefined functions, or missing imports.

Request: {prompt}

{f"Context from codebase: {context}" if context else ""}

CRITICAL REQUIREMENTS:
- Write COMPLETE, SELF-CONTAINED Python code that runs immediately
- NO placeholders like "load_data()" or "undefined_function()"
- NO missing imports - include ALL necessary imports at the top
- NO TODO comments or incomplete functions
- DEFINE ALL functions and classes that are used
- Include ALL data generation, processing, and output in one file
- The code must execute successfully from start to finish
- If you need data, GENERATE it in the code (don't load from external files)
- If you need libraries, use only standard library (random, math, etc.) or generate alternatives
- Include proper error handling and docstrings
- Add a main section that demonstrates the functionality

EXAMPLE: If asked for "neural network", create the dataset generation, network class, training loop, and evaluation ALL in one complete file.

Generate ONLY the complete Python code that runs without errors: [/INST]

```python"""

            data = {
                'model': self.model,
                'prompt': full_prompt,
                'stream': False,
                'options': {
                    'temperature': 0.1,
                    'top_p': 0.9,
                    'stop': ['```', '</s>']
                }
            }

            response = requests.post(
                f"{self.base_url}/api/generate",
                json=data,
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()
                code = result['response'].strip()

                # Clean up the response
                if code.startswith('```python'):
                    code = code[9:]
                if code.endswith('```'):
                    code = code[:-3]

                return code.strip()
            else:
                print(f"Ollama API error: {response.status_code}")
                return self._fallback_local_response(prompt)

        except Exception as e:
            print(f"Error calling Ollama: {e}")
            return self._fallback_local_response(prompt)

    def analyze_code(self, code: str, question: str) -> str:
        """Analyze code using local Ollama."""
        try:
            prompt = f"""<s>[INST] You are an expert code analyst. Analyze the following Python code and answer the question.

Code:
```python
{code}
```

Question: {question}

Provide a clear, detailed analysis: [/INST]"""

            data = {
                'model': self.model,
                'prompt': prompt,
                'stream': False,
                'options': {
                    'temperature': 0.1,
                    'top_p': 0.9
                }
            }

            response = requests.post(
                f"{self.base_url}/api/generate",
                json=data,
                timeout=90
            )

            if response.status_code == 200:
                result = response.json()
                return result['response'].strip()
            else:
                return f"Ollama service error: {response.status_code}"

        except Exception as e:
            return f"Error with Ollama: {e}"

    def _fallback_local_response(self, prompt: str) -> str:
        """Fallback when Ollama is not available."""
        return f"""# Generated code for: {prompt}
# Note: Ollama not available. Install Ollama and run 'ollama pull codellama' for local code generation.

def placeholder_function():
    \"\"\"
    This is a placeholder function.
    To get local code generation:
    1. Install Ollama (https://ollama.ai)
    2. Run: ollama pull codellama
    3. Start Ollama service
    \"\"\"
    pass
"""


class LLMManager:
    """Manages LLM providers and code generation."""

    def __init__(self):
        self.providers = {
            'openai': OpenAIProvider(),
            'ollama': OllamaProvider()
        }
        self.default_provider = 'ollama'  # Default to local Ollama

    def set_provider(self, provider_name: str):
        """Set the active LLM provider."""
        if provider_name in self.providers:
            self.default_provider = provider_name
        else:
            raise ValueError(f"Unknown provider: {provider_name}")

    def generate_code(self, prompt: str, context: str = "", provider: str = None) -> str:
        """Generate code using the specified or default provider."""
        provider_name = provider or self.default_provider
        provider_obj = self.providers[provider_name]
        return provider_obj.generate_code(prompt, context)

    def analyze_code(self, code: str, question: str, provider: str = None) -> str:
        """Analyze code using the specified or default provider."""
        provider_name = provider or self.default_provider
        provider_obj = self.providers[provider_name]
        return provider_obj.analyze_code(code, question)

    def plan_changes(self, request: str, codebase_context: str) -> List[CodeChange]:
        """Plan what changes need to be made to fulfill a request."""

        # First, analyze what files exist in the context
        existing_files = []
        if codebase_context:
            # Extract file information from context
            lines = codebase_context.split('\n')
            for line in lines:
                if line.startswith('File:'):
                    file_name = line.replace('File:', '').strip()
                    if file_name and file_name != 'unknown':
                        existing_files.append(file_name)

        # Generate the actual code for the request
        code_prompt = f"""Generate Python code for this request: {request}

{f"Existing codebase context: {codebase_context}" if codebase_context else ""}

Generate complete, working Python code with proper error handling and documentation."""

        generated_code = self.generate_code(code_prompt)

        # Determine the best file to modify or create
        if existing_files:
            # If we have existing files, try to modify the most relevant one
            target_file = existing_files[0]  # Use the first relevant file
            action = "modify"
            description = f"Add functionality: {request}"
        else:
            # Create a new file
            if "calculator" in request.lower() or "math" in request.lower():
                target_file = "calculator.py"
            elif "test" in request.lower():
                target_file = "test_functions.py"
            elif "util" in request.lower():
                target_file = "utilities.py"
            else:
                target_file = "new_feature.py"
            action = "create"
            description = f"Create new file for: {request}"

        return [CodeChange(
            file_path=target_file,
            action=action,
            content=generated_code,
            description=description
        )]
