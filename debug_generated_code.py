"""
Debug what's wrong with the generated code.
"""

from core.working_augment_engine import WorkingAugmentEngine

def debug_neural_network_code():
    """Debug the neural network code generation."""
    print("🔍 DEBUGGING NEURAL NETWORK CODE")
    print("=" * 50)
    
    engine = WorkingAugmentEngine()
    analysis = engine._analyze_task("create a simple neural network")
    code = engine._generate_neural_network_code()
    
    print("Generated code:")
    print("-" * 30)
    print(code[:500] + "..." if len(code) > 500 else code)
    print("-" * 30)
    
    # Try to compile it
    try:
        compile(code, '<string>', 'exec')
        print("✅ Code compiles successfully!")
    except SyntaxError as e:
        print(f"❌ Syntax Error: {e}")
        print(f"Line {e.lineno}: {e.text}")
    except Exception as e:
        print(f"❌ Other Error: {e}")

def debug_web_scraper_code():
    """Debug the web scraper code generation."""
    print("\n🔍 DEBUGGING WEB SCRAPER CODE")
    print("=" * 50)
    
    engine = WorkingAugmentEngine()
    code = engine._generate_web_scraper_code()
    
    print("Generated code (first 500 chars):")
    print("-" * 30)
    print(code[:500])
    print("-" * 30)
    
    # Try to compile it
    try:
        compile(code, '<string>', 'exec')
        print("✅ Code compiles successfully!")
    except SyntaxError as e:
        print(f"❌ Syntax Error: {e}")
        print(f"Line {e.lineno}: {e.text}")
        print(f"Error at position {e.offset}")
    except Exception as e:
        print(f"❌ Other Error: {e}")

def test_simple_working_code():
    """Test if a simple version works."""
    print("\n🧪 TESTING SIMPLE WORKING CODE")
    print("=" * 50)
    
    simple_code = '''
import random
import math

def create_dataset():
    data = []
    labels = []
    for i in range(100):
        x1 = random.uniform(-2, 2)
        x2 = random.uniform(-2, 2)
        label = 1 if x1**2 + x2**2 < 1.5 else 0
        data.append([x1, x2])
        labels.append(label)
    return data, labels

def main():
    print("Simple neural network demo")
    data, labels = create_dataset()
    print(f"Created {len(data)} samples")
    accuracy = sum(labels) / len(labels)
    print(f"Baseline accuracy: {accuracy:.2%}")

if __name__ == "__main__":
    main()
'''
    
    print("Testing simple code:")
    print("-" * 30)
    print(simple_code)
    print("-" * 30)
    
    # Try to compile and execute
    try:
        compile(simple_code, '<string>', 'exec')
        print("✅ Simple code compiles!")
        
        # Try to execute
        exec(simple_code)
        print("✅ Simple code executes!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_neural_network_code()
    debug_web_scraper_code()
    test_simple_working_code()
