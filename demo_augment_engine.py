#!/usr/bin/env python3
"""
Demo script showcasing the TRUE AUGMENT-STYLE CODING ENGINE

This demonstrates the complete Augment Code behavior:
1. Understand entire codebase
2. Write code according to prompt  
3. Execute code and read terminal output
4. If errors occur, correct code based on error messages
5. Repeat iteratively until success

This is exactly how Augment Code works!
"""

import os
import sys
from pathlib import Path

# Add core to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

from core.augment_engine import AugmentEngine
from rich.console import Console
from rich.panel import Panel

console = Console()

def demo_augment_engine():
    """Demonstrate the Augment Engine with various coding tasks."""
    
    console.print(Panel(
        "🤖 **AUGMENT ENGINE DEMONSTRATION**\n\n"
        "This demo shows TRUE Augment-style behavior:\n"
        "• 🧠 Understand entire codebase\n"
        "• 🎨 Generate code from natural language\n"
        "• ⚡ Execute code in terminal\n"
        "• 📖 Read terminal output and errors\n"
        "• 🔧 Self-correct based on errors\n"
        "• 🔄 Iterate until success\n\n"
        "Just like the real Augment Code!",
        title="🚀 Augment Engine Demo",
        border_style="bold blue"
    ))
    
    # Initialize engine
    engine = AugmentEngine()
    demo_project = Path("./examples/demo_project")
    
    # Demo tasks that showcase different capabilities
    demo_tasks = [
        {
            "name": "Simple Hello World",
            "prompt": "create a function that prints 'Hello from Augment!' and call it",
            "description": "Basic code generation and execution"
        },
        {
            "name": "Mathematical Function",
            "prompt": "create a function that calculates the factorial of a number and test it with factorial(5)",
            "description": "More complex logic with testing"
        },
        {
            "name": "Error Handling",
            "prompt": "create a function that safely divides two numbers with proper error handling and test it",
            "description": "Demonstrates error handling and edge cases"
        },
        {
            "name": "Data Processing",
            "prompt": "create a function that finds the maximum number in a list and test it with [1, 5, 3, 9, 2]",
            "description": "List processing and algorithms"
        }
    ]
    
    console.print(f"\n[bold green]🎯 Running {len(demo_tasks)} demonstration tasks...[/bold green]\n")
    
    results = []
    
    for i, task in enumerate(demo_tasks, 1):
        console.print(f"[bold cyan]📋 Task {i}: {task['name']}[/bold cyan]")
        console.print(f"[dim]{task['description']}[/dim]")
        console.print(f"[yellow]Prompt: {task['prompt']}[/yellow]\n")
        
        try:
            # Run the Augment engine
            session = engine.augment_code(
                prompt=task['prompt'],
                codebase_path=demo_project,
                execute_in_terminal=True
            )
            
            results.append({
                'task': task['name'],
                'success': session.final_success,
                'iterations': session.total_iterations,
                'time': session.execution_time,
                'code_length': len(session.final_code)
            })
            
            if session.final_success:
                console.print(f"[bold green]✅ Task {i} completed successfully![/bold green]\n")
            else:
                console.print(f"[bold red]❌ Task {i} failed after {session.total_iterations} iterations[/bold red]\n")
                
        except Exception as e:
            console.print(f"[red]❌ Task {i} failed with error: {e}[/red]\n")
            results.append({
                'task': task['name'],
                'success': False,
                'iterations': 0,
                'time': 0,
                'code_length': 0
            })
        
        console.print("─" * 80 + "\n")
    
    # Display final summary
    display_demo_summary(results)

def display_demo_summary(results):
    """Display a summary of all demo results."""
    from rich.table import Table
    
    console.print(Panel(
        "📊 **AUGMENT ENGINE DEMONSTRATION COMPLETE**\n\n"
        "Summary of all tasks and their results:",
        title="🏆 Demo Summary",
        border_style="bold green"
    ))
    
    # Create results table
    table = Table(title="📈 Task Results")
    table.add_column("Task", style="cyan")
    table.add_column("Success", style="green")
    table.add_column("Iterations", style="yellow")
    table.add_column("Time (s)", style="blue")
    table.add_column("Code Length", style="dim")
    
    total_tasks = len(results)
    successful_tasks = sum(1 for r in results if r['success'])
    total_time = sum(r['time'] for r in results)
    
    for result in results:
        success_icon = "✅" if result['success'] else "❌"
        table.add_row(
            result['task'],
            success_icon,
            str(result['iterations']),
            f"{result['time']:.2f}",
            str(result['code_length'])
        )
    
    console.print(table)
    
    # Overall statistics
    success_rate = (successful_tasks / total_tasks) * 100 if total_tasks > 0 else 0
    
    console.print(Panel(
        f"🎯 **OVERALL PERFORMANCE**\n\n"
        f"• Tasks Completed: {successful_tasks}/{total_tasks}\n"
        f"• Success Rate: {success_rate:.1f}%\n"
        f"• Total Time: {total_time:.2f} seconds\n"
        f"• Average Time per Task: {total_time/total_tasks:.2f} seconds\n\n"
        f"🚀 **The Augment Engine successfully demonstrates:**\n"
        f"✅ Complete codebase understanding\n"
        f"✅ Natural language to code generation\n"
        f"✅ Real terminal execution\n"
        f"✅ Error detection and analysis\n"
        f"✅ Iterative self-correction\n"
        f"✅ Production-ready code output\n\n"
        f"This is TRUE Augment-style coding behavior!",
        title="🏆 Final Results",
        border_style="bold green"
    ))

def quick_demo():
    """Run a quick single-task demo."""
    console.print(Panel(
        "🚀 **QUICK AUGMENT DEMO**\n\n"
        "Running a single task to demonstrate core functionality...",
        title="⚡ Quick Demo",
        border_style="bold yellow"
    ))
    
    engine = AugmentEngine()
    demo_project = Path("./examples/demo_project")
    
    session = engine.augment_code(
        prompt="create a function that adds two numbers and test it with add(3, 5)",
        codebase_path=demo_project,
        execute_in_terminal=True
    )
    
    if session.final_success:
        console.print(Panel(
            f"🎉 **QUICK DEMO SUCCESS!**\n\n"
            f"✅ Generated working code in {session.total_iterations} iteration(s)\n"
            f"⏱️  Completed in {session.execution_time:.2f} seconds\n"
            f"📝 Code length: {len(session.final_code)} characters\n\n"
            f"The Augment Engine is working perfectly!",
            title="✅ Success",
            border_style="bold green"
        ))
    else:
        console.print(Panel(
            f"⚠️ Quick demo incomplete after {session.total_iterations} iterations",
            title="⚠️ Incomplete",
            border_style="bold yellow"
        ))

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Augment Engine Demonstration")
    parser.add_argument("--quick", action="store_true", help="Run quick single-task demo")
    parser.add_argument("--full", action="store_true", help="Run full multi-task demo")
    
    args = parser.parse_args()
    
    if args.quick:
        quick_demo()
    elif args.full:
        demo_augment_engine()
    else:
        # Default to quick demo
        quick_demo()
