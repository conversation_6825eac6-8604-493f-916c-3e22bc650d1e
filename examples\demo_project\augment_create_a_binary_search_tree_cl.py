class BinarySearchTree:
    """A binary search tree class with insert, search, delete, in-order traversal, pre-order traversal, post-order traversal, find minimum, find maximum, and tree height methods.

    Attributes:
        root (Node): The root node of the tree.

    Methods:
        __init__(): Initializes an empty binary search tree.
        insert(value): Inserts a new value into the tree.
        search(value): Searches for a value in the tree.
        delete(value): Deletes a value from the tree.
        in_order_traversal(): Performs an in-order traversal of the tree.
        pre_order_traversal(): Performs a pre-order traversal of the tree.
        post_order_traversal(): Performs a post-order traversal of the tree.
        find_minimum(): Finds the minimum value in the tree.
        find_maximum(): Finds the maximum value in the tree.
        tree_height(): Calculates the height of the tree.
    """

    class Node:
        """A node in the binary search tree."""

        def __init__(self, value):
            self.value = value
            self.left = None
            self.right = None

    def __init__(self):
        self.root = None

    def insert(self, value):
        """Inserts a new value into the tree."""
        if self.root is None:
            self.root = BinarySearchTree.Node(value)
        else:
            current_node = self.root
            while True:
                if value < current_node.value:
                    if current_node.left is None:
                        current_node.left = BinarySearchTree.Node(value)
                        break
                    else:
                        current_node = current_node.left
                elif value > current_node.value:
                    if current_node.right is None:
                        current_node.right = BinarySearchTree.Node(value)
                        break
                    else:
                        current_node = current_node.right
                else:
                    raise ValueError("Value already exists in the tree.")

    def search(self, value):
        """Searches for a value in the tree."""
        if self.root is None:
            return False
        else:
            current_node = self.root
            while True:
                if value < current_node.value:
                    if current_node.left is None:
                        return False
                    else:
                        current_node = current_node.left
                elif value > current_node.value:
                    if current_node.right is None:
                        return False
                    else:
                        current_node = current_node.right
                else:
                    return True

    def delete(self, value):
        """Deletes a value from the tree."""
        if self.root is None:
            raise ValueError("Tree is empty.")
        else:
            current_node = self.root
            parent_node = None
            while True:
                if value < current_node.value:
                    if current_node.left is None:
                        return False
                    else:
                        parent_node = current_node
                        current_node = current_node.left
                elif value > current_node.value:
                    if current_node.right is None:
                        return False
                    else:
                        parent_node = current_node
                        current_node = current_node.right
                else:
                    break
            if current_node.left is None and current_node.right is None:
                if parent_node is not None:
                    if parent_node.left == current_node:
                        parent_node.left = None
                    elif parent_node.right == current_node:
                        parent_node.right = None
            elif current_node.left is not None and current_node.right is not None:
                successor = current_node.right
                while successor.left is not None:
                    successor = successor.left
                current_node.value = successor.value
                self.delete(successor.value)
            else:
                child_node = current_node.left if current_node.left is not None else current_node.right
                if parent_node is not None:
                    if parent_node.left == current_node:
                        parent_node.left = child_node
                    elif parent_node.right == current_node:
                        parent_node.right = child_node
                else:
                    self.root = child_node

    def in_order_traversal(self):
        """Performs an in-order traversal of the tree."""
        if self.root is None:
            return []
        else:
            result = []
            stack = [self.root]
            while len(stack) > 0:
                node = stack.pop()
                result.append(node.value)
                if node.right is not None:
                    stack.append(node.right)
                if node.left is not None:
                    stack.append(node.left)
            return result

    def pre_order_traversal(self):
        """Performs a pre-order traversal of the tree."""
        if self.root is None:
            return []
        else:
            result = [self.root.value]
            stack = [self.root.left, self.root.right]
            while len(stack) > 0:
                node = stack.pop()
                if node is not None:
                    result.append(node.value)
                    stack.extend([node.left, node.right])
            return result

    def post_order_traversal(self):
        """Performs a post-order traversal of the tree."""
        if self.root is None:
            return []
        else:
            result = []
            stack = [self.root]
            while len(stack) > 0:
                node = stack.pop()
                if node.left is not None:
                    stack.append(node.left)
                if node.right is not None:
                    stack.append(node.right)
                result.append(node.value)
            return result

    def find_minimum(self):
        """Finds the minimum value in the tree."""
        if self.root is None:
            raise ValueError("Tree is empty.")
        else:
            current_node = self.root
            while current_node.left is not None:
                current_node = current_node.left
            return current_node.value

    def find_maximum(self):
        """Finds the maximum value in the tree."""
        if self.root is None:
            raise ValueError("Tree is empty.")
        else:
            current_node = self.root
            while current_node.right is not None:
                current_node = current_node.right
            return current_node.value

    def tree_height(self):
        """Calculates the height of the tree."""
        if self.root is None:
            return 0
        else:
            left_height = self.tree_height(self.root.left)
            right_height = self.tree_height(self.root.right)
            return 1 + max(left_height, right_height)