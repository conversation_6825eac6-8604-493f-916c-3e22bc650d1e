# calculator.py
class Calculator:
    """A simple calculator class with basic and advanced operations."""

    def __init__(self):
        self.result = 0

    def add(self, x, y):
        """Add two numbers together."""
        try:
            self.result = float(x) + float(y)
        except ValueError as e:
            print("Invalid input:", e)
            return None
        return self.result

    def subtract(self, x, y):
        """Subtract one number from another."""
        try:
            self.result = float(x) - float(y)
        except ValueError as e:
            print("Invalid input:", e)
            return None
        return self.result

    def multiply(self, x, y):
        """Multiply two numbers together."""
        try:
            self.result = float(x) * float(y)
        except ValueError as e:
            print("Invalid input:", e)
            return None
        return self.result

    def divide(self, x, y):
        """Divide one number by another."""
        try:
            self.result = float(x) / float(y)
        except ValueError as e:
            print("Invalid input:", e)
            return None
        return self.result

    def power(self, x, y):
        """Raise a number to a power."""
        try:
            self.result = float(x) ** float(y)
        except ValueError as e:
            print("Invalid input:", e)
            return None
        return self.result

    def square_root(self, x):
        """Find the square root of a number."""
        try:
            self.result = math.sqrt(float(x))
        except ValueError as e:
            print("Invalid input:", e)
            return None
        return self.result

    def factorial(self, x):
        """Find the factorial of a number."""
        try:
            self.result = math.factorial(int(x))
        except ValueError as e:
            print("Invalid input:", e)
            return None
        return self.result