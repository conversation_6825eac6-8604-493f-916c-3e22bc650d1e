import os
import hashlib
import json
from mimetypes import MimeTypes

class FileScanner:
    """Recursively scans directories and returns a list of files."""
    
    def __init__(self, directory):
        self.directory = directory
    
    def scan(self):
        file_list = []
        for root, dirs, files in os.walk(self.directory):
            for file in files:
                file_path = os.path.join(root, file)
                file_list.append(file_path)
        return file_list
    
class FileAnalyzer:
    """Analyses the size and type of files."""
    
    def __init__(self, file_list):
        self.file_list = file_list
    
    def analyze(self):
        file_info_dict = {}
        for file in self.file_list:
            with open(file, 'rb') as f:
                file_size = os.path.getsize(file)
                file_type = MimeTypes().guess_type(file)[0]
                file_info_dict[file] = {'size': file_size, 'type': file_type}
        return file_info_dict
    
class DuplicateFinder:
    """Finds duplicate files using hashing."""
    
    def __init__(self, file_list):
        self.file_list = file_list
    
    def find(self):
        hash_dict = {}
        for file in self.file_list:
            with open(file, 'rb') as f:
                file_hash = hashlib.sha256(f.read()).hexdigest()
                if file_hash in hash_dict:
                    hash_dict[file_hash].append(file)
                else:
                    hash_dict[file_hash] = [file]
        return hash_dict
    
class ReportGenerator:
    """Creates detailed reports."""
    
    def __init__(self, file_list):
        self.file_list = file_list
    
    def generate(self):
        report_dict = {}
        for file in self.file_list:
            with open(file, 'rb') as f:
                file_size = os.path.getsize(file)
                file_type = MimeTypes().guess_type(file)[0]
                report_dict[file] = {'size': file_size, 'type': file_type}
        return json.dumps(report_dict, indent=4)
    
def main():
    directory = os.getcwd()
    scanner = FileScanner(directory)
    file_list = scanner.scan()
    analyzer = FileAnalyzer(file_list)
    file_info_dict = analyzer.analyze()
    duplicate_finder = DuplicateFinder(file_list)
    hash_dict = duplicate_finder.find()
    report_generator = ReportGenerator(file_list)
    report = report_generator.generate()
    print(report)
    
if __name__ == '__main__':
    main()