import numpy as np

class MatrixOperations:
    """
    Class for performing matrix operations such as addition, multiplication, and determinant.
    """

    def __init__(self, matrix):
        self.matrix = matrix

    def add(self, other_matrix):
        """
        Adds two matrices together.

        Parameters:
            other_matrix (MatrixOperations): The second matrix to be added.

        Returns:
            MatrixOperations: The resulting sum of the two matrices.
        """
        return MatrixOperations(self.matrix + other_matrix.matrix)

    def multiply(self, other_matrix):
        """
        Multiplies two matrices together.

        Parameters:
            other_matrix (MatrixOperations): The second matrix to be multiplied.

        Returns:
            MatrixOperations: The resulting product of the two matrices.
        """
        return MatrixOperations(np.dot(self.matrix, other_matrix.matrix))

    def determinant(self):
        """
        Calculates the determinant of a matrix.

        Returns:
            float: The determinant of the matrix.
        """
        return np.linalg.det(self.matrix)

    def inverse(self):
        """
        Calculates the inverse of a matrix.

        Returns:
            MatrixOperations: The inverse of the matrix.
        """
        return MatrixOperations(np.linalg.inv(self.matrix))

class VectorOperations:
    """
    Class for performing vector operations such as dot product, cross product, and normalization.
    """

    def __init__(self, vector):
        self.vector = vector

    def dot_product(self, other_vector):
        """
        Calculates the dot product of two vectors.

        Parameters:
            other_vector (VectorOperations): The second vector to be multiplied.

        Returns:
            float: The resulting dot product of the two vectors.
        """
        return np.dot(self.vector, other_vector.vector)

    def cross_product(self, other_vector):
        """
        Calculates the cross product of two vectors.

        Parameters:
            other_vector (VectorOperations): The second vector to be multiplied.

        Returns:
            VectorOperations: The resulting cross product of the two vectors.
        """
        return VectorOperations(np.cross(self.vector, other_vector.vector))

    def normalize(self):
        """
        Normalizes a vector to have a length of 1.

        Returns:
            VectorOperations: The normalized vector.
        """
        return VectorOperations(self.vector / np.linalg.norm(self.vector))

class NumericalMethods:
    """
    Class for performing numerical methods such as Newton-Raphson root finding and numerical integration using Simpson's rule.
    """

    def __init__(self, function):
        self.function = function

    def newton_raphson(self, x0, tol=1e-6, max_iter=50):
        """
        Uses Newton-Raphson's method to find the root of a function.

        Parameters:
            x0 (float): The initial guess for the root.
            tol (float): The tolerance for the root. Default is 1e-6.
            max_iter (int): The maximum number of iterations. Default is 50.

        Returns:
            float: The root of the function.
        """
        x = x0
        for i in range(max_iter):
            fx = self.function(x)
            if abs(fx) < tol:
                return x
            x -= fx / self.function.derivative(x)
        raise RuntimeError("Maximum number of iterations reached.")

    def simpson_integration(self, a, b, n=100):
        """
        Uses Simpson's rule to approximate the definite integral of a function.

        Parameters:
            a (float): The lower bound of the integration range.
            b (float): The upper bound of the integration range.
            n (int): The number of subintervals. Default is 100.

        Returns:
            float: The approximate value of the definite integral.
        """
        h = (b - a) / n
        x = np.linspace(a, b, n + 1)
        y = self.function(x)
        return (h / 3) * (y[0] + y[-1] + 2 * sum(y[1:-1]))