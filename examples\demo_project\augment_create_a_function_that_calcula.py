import math

def calculate_square_root(number):
    """
    Calculates the square root of a number using the math.sqrt function.

    Parameters:
        number (int or float): The number to calculate the square root for.

    Returns:
        float: The square root of the given number.
    """
    try:
        result = math.sqrt(number)
    except ValueError as e:
        print("Invalid input. Please enter a positive number.")
        return None
    else:
        return result

if __name__ == "__main__":
    # Test the function with the number 16
    print(calculate_square_root(16))