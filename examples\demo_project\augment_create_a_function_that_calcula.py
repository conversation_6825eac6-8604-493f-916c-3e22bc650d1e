import math

def calculate_circle_area(radius):
    """
    Calculates the area of a circle given its radius.
    
    Parameters:
    radius (float): The radius of the circle.
    
    Returns:
    float: The area of the circle.
    """
    try:
        radius = float(radius)
    except ValueError:
        raise ValueError("Radius must be a number.")
    if radius < 0:
        raise ValueError("Radius must be non-negative.")
    return math.pi * radius ** 2

# Test the function with radius 5
print(calculate_circle_area(5))