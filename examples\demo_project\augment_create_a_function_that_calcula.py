import math

def fibon<PERSON>ci(n):
    """
    Calculates the nth Fibonacci number.
    
    Args:
        n (int): The index of the <PERSON><PERSON><PERSON><PERSON> number to calculate.
    
    Returns:
        int: The calculated <PERSON><PERSON><PERSON>ci number.
    """
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)

# Test the function with a sample input
print(<PERSON><PERSON><PERSON><PERSON>(10)) # Should print 55