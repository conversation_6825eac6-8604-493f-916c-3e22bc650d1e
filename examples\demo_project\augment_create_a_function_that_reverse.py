def reverse_string_and_count_vowels(input_string):
    """Reverses a string and counts the number of vowels in it.

    Args:
        input_string (str): The input string to be reversed and analyzed.

    Returns:
        tuple: A tuple containing the reversed string and the number of vowels in it.
    """
    # Reverse the input string
    reversed_string = input_string[::-1]

    # Count the number of vowels in the reversed string
    vowel_count = 0
    for char in reversed_string:
        if char in "aeiou":
            vowel_count += 1

    return (reversed_string, vowel_count)

# Test the function with the input string 'Hello World'
print(reverse_string_and_count_vowels("Hello World"))