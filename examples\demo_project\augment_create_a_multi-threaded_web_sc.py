import threading
import queue
import random
import time

class WebScraperSimulator:
    """
    A multi-threaded web scraper simulator that uses threading to process multiple URLs concurrently.
    It includes a thread pool, queue management, rate limiting, and result aggregation.
    """

    def __init__(self, urls):
        self.urls = urls
        self.queue = queue.Queue()
        self.thread_pool = []
        self.results = {}

    def start(self):
        for url in self.urls:
            self.queue.put(url)

        for i in range(len(self.urls)):
            thread = threading.Thread(target=self.scrape_url, args=(i,))
            self.thread_pool.append(thread)
            thread.start()

    def scrape_url(self, index):
        url = self.queue.get()
        try:
            # Simulate random delays and success/failure scenarios
            delay = random.uniform(0, 5)
            time.sleep(delay)
            if random.random() < 0.5:
                raise Exception("Simulated failure")
            else:
                self.results[url] = "Success"
        except Exception as e:
            self.results[url] = f"Failed with error: {e}"
        finally:
            self.queue.task_done()

    def join(self):
        for thread in self.thread_pool:
            thread.join()

if __name__ == "__main__":
    urls = ["https://www.example1.com", "https://www.example2.com", "https://www.example3.com"]
    scraper = WebScraperSimulator(urls)
    scraper.start()
    scraper.join()
    print(scraper.results)