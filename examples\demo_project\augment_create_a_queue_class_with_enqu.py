class Queue:
    """A generic queue class with enqueue, dequeue, peek, is_empty, and size methods.

    Attributes:
        data (list): The underlying list to store the elements in the queue.
        priority (bool): Whether the queue is a priority queue or not.

    Methods:
        enqueue(element)
            Add an element to the back of the queue.

        dequeue()
            Remove and return the front element of the queue.

        peek()
            Return the front element of the queue without removing it.

        is_empty()
            Check if the queue is empty.

        size()
            Return the number of elements in the queue.
    """

    def __init__(self, priority=False):
        self.data = []
        self.priority = priority

    def enqueue(self, element):
        """Add an element to the back of the queue."""
        if not self.priority:
            self.data.append(element)
        else:
            for i in range(len(self.data)):
                if element > self.data[i]:
                    self.data.insert(i, element)
                    break
            else:
                self.data.append(element)

    def dequeue(self):
        """Remove and return the front element of the queue."""
        if len(self.data) == 0:
            raise IndexError("Queue is empty")
        return self.data.pop(0)

    def peek(self):
        """Return the front element of the queue without removing it."""
        if len(self.data) == 0:
            raise IndexError("Queue is empty")
        return self.data[0]

    def is_empty(self):
        """Check if the queue is empty."""
        return len(self.data) == 0

    def size(self):
        """Return the number of elements in the queue."""
        return len(self.data)