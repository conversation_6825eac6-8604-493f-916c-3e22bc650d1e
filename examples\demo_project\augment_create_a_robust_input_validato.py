import re

class InputValidator:
    """
    A class for validating input data.

    Attributes:
        email_regex (str): A regular expression pattern for matching email addresses.
        phone_regex (str): A regular expression pattern for matching phone numbers.
        credit_card_regex (str): A regular expression pattern for matching credit card numbers.
        url_regex (str): A regular expression pattern for matching URLs.
    """

    def __init__(self):
        self.email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        self.phone_regex = r"^\d{3}-\d{3}-\d{4}$"
        self.credit_card_regex = r"^(?:5[1-5][0-9]{14}|6(?:011|5[0-9][0-9])[0-9]{12})$"
        self.url_regex = r"^https?://(?:[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}|localhost)(?::\d+)?(?:[^:]*)$"

    def validate_email(self, email):
        """
        Validates an email address.

        Args:
            email (str): The email address to validate.

        Returns:
            bool: True if the email is valid, False otherwise.
        """
        return re.match(self.email_regex, email) is not None

    def validate_phone(self, phone):
        """
        Validates a phone number.

        Args:
            phone (str): The phone number to validate.

        Returns:
            bool: True if the phone number is valid, False otherwise.
        """
        return re.match(self.phone_regex, phone) is not None

    def validate_credit_card(self, credit_card):
        """
        Validates a credit card number.

        Args:
            credit_card (str): The credit card number to validate.

        Returns:
            bool: True if the credit card number is valid, False otherwise.
        """
        return re.match(self.credit_card_regex, credit_card) is not None

    def validate_url(self, url):
        """
        Validates a URL.

        Args:
            url (str): The URL to validate.

        Returns:
            bool: True if the URL is valid, False otherwise.
        """
        return re.match(self.url_regex, url) is not None

    def validate_input(self, input_data):
        """
        Validates input data against a list of regular expressions.

        Args:
            input_data (str): The input data to validate.

        Returns:
            bool: True if the input data is valid, False otherwise.
        """
        for regex in [self.email_regex, self.phone_regex, self.credit_card_regex, self.url_regex]:
            if re.match(regex, input_data) is not None:
                return True
        return False