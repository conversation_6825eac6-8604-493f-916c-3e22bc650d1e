def bubble_sort(my_list):
    """
    Sorts a list of numbers using the bubble sort algorithm.
    
    Args:
        my_list (list): The list to be sorted.
        
    Returns:
        list: The sorted list.
    """
    n = len(my_list)
    for i in range(n-1):
        for j in range(0, n-i-1):
            if my_list[j] > my_list[j+1]:
                my_list[j], my_list[j+1] = my_list[j+1], my_list[j]
    return my_list

# Test the bubble sort function with a list of numbers
my_list = [64, 34, 25, 12, 22, 11, 90]
sorted_list = bubble_sort(my_list)
print("Sorted list:", sorted_list)