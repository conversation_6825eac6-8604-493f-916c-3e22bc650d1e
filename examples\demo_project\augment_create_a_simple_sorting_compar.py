import time

def bubble_sort(arr):
    """Bubble sort algorithm"""
    n = len(arr)
    for i in range(n-1):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

def selection_sort(arr):
    """Selection sort algorithm"""
    n = len(arr)
    for i in range(n-1):
        min_idx = i
        for j in range(i+1, n):
            if arr[j] < arr[min_idx]:
                min_idx = j
        arr[i], arr[min_idx] = arr[min_idx], arr[i]
    return arr

def sort_comparison(arr):
    """Compares bubble and selection sort algorithms"""
    start_time = time.time()
    sorted_arr = bubble_sort(arr)
    end_time = time.time() - start_time
    print("Bubble sort took", end_time, "seconds")
    
    start_time = time.time()
    sorted_arr = selection_sort(arr)
    end_time = time.time() - start_time
    print("Selection sort took", end_time, "seconds")

if __name__ == "__main__":
    arr = [64, 34, 25, 12, 22, 11, 90]
    sort_comparison(arr)