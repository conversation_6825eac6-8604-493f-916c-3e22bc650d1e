class Stack:
    """A simple stack data structure class with push, pop, peek, is_empty, and size methods.

    Attributes:
        items (list): The list of items in the stack.

    Methods:
        push(item): Add an item to the top of the stack.
        pop(): Remove and return the item at the top of the stack.
        peek(): Return the item at the top of the stack without removing it.
        is_empty(): Check if the stack is empty.
        size(): Return the number of items in the stack.
    """

    def __init__(self):
        self.items = []

    def push(self, item):
        """Add an item to the top of the stack."""
        self.items.append(item)

    def pop(self):
        """Remove and return the item at the top of the stack."""
        if len(self.items) == 0:
            raise ValueError("Stack is empty")
        return self.items.pop()

    def peek(self):
        """Return the item at the top of the stack without removing it."""
        if len(self.items) == 0:
            raise ValueError("Stack is empty")
        return self.items[-1]

    def is_empty(self):
        """Check if the stack is empty."""
        return len(self.items) == 0

    def size(self):
        """Return the number of items in the stack."""
        return len(self.items)