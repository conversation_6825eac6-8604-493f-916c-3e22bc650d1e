import csv
import json

class DataAnalyzer:
    """Simple data analyzer using standard library."""

    def __init__(self):
        self.data = []

    def load_csv(self, filename):
        """Load data from CSV file."""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                self.data = list(reader)
            print(f"Loaded {len(self.data)} rows from {filename}")
        except Exception as e:
            print(f"Error loading {filename}: {e}")

    def analyze_data(self):
        """Perform basic statistical analysis."""
        if not self.data:
            return {}

        # Find numeric columns
        numeric_cols = []
        for key in self.data[0].keys():
            try:
                float(self.data[0][key])
                numeric_cols.append(key)
            except ValueError:
                pass

        # Calculate statistics
        stats = {}
        for col in numeric_cols:
            values = []
            for row in self.data:
                try:
                    values.append(float(row[col]))
                except ValueError:
                    pass

            if values:
                stats[col] = {
                    'mean': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'count': len(values)
                }

        return stats

def main():
    print("Data Analysis Demo")
    print("=" * 30)

    # Create sample data
    sample_data = [
        {"name": "Alice", "age": "25", "score": "85.5"},
        {"name": "Bob", "age": "30", "score": "92.0"},
        {"name": "Charlie", "age": "35", "score": "78.5"},
        {"name": "Diana", "age": "28", "score": "88.0"},
        {"name": "Eve", "age": "32", "score": "91.5"}
    ]

    with open("sample_data.csv", "w", newline="", encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=["name", "age", "score"])
        writer.writeheader()
        writer.writerows(sample_data)

    # Analyze data
    analyzer = DataAnalyzer()
    analyzer.load_csv("sample_data.csv")
    results = analyzer.analyze_data()

    # Display results
    print("\nAnalysis Results:")
    print("=" * 20)
    for col, values in results.items():
        print(f"\n{col}:")
        for stat, value in values.items():
            if isinstance(value, float):
                print(f"  {stat:8}: {value:.2f}")
            else:
                print(f"  {stat:8}: {value}")

    print("\nAnalysis completed!")

if __name__ == "__main__":
    main()
