class Game:
    """Simple console game."""

    def __init__(self):
        self.running = True
        self.player_pos = [5, 5]
        self.score = 0
        self.board_size = 10
        self.board = [[' ' for _ in range(self.board_size)] for _ in range(self.board_size)]

        # Add some obstacles
        self.board[2][3] = '#'
        self.board[7][6] = '#'
        self.board[4][8] = '#'

        # Add collectibles
        self.board[1][1] = '*'
        self.board[8][2] = '*'
        self.board[3][7] = '*'

    def render(self):
        """Render the game board."""
        print("\n" + "=" * (self.board_size + 2))
        print(f"Score: {self.score}")
        print("=" * (self.board_size + 2))

        for i in range(self.board_size):
            line = "|"
            for j in range(self.board_size):
                if [i, j] == self.player_pos:
                    line += "P"
                else:
                    line += self.board[i][j]
            line += "|"
            print(line)

        print("=" * (self.board_size + 2))
        print("Controls: W/A/S/D to move, Q to quit")

    def handle_input(self):
        """Handle player input."""
        try:
            move = input("Enter move: ").lower().strip()

            if move == 'q':
                self.running = False
                return

            new_pos = self.player_pos.copy()

            if move == 'w' and self.player_pos[0] > 0:
                new_pos[0] -= 1
            elif move == 's' and self.player_pos[0] < self.board_size - 1:
                new_pos[0] += 1
            elif move == 'a' and self.player_pos[1] > 0:
                new_pos[1] -= 1
            elif move == 'd' and self.player_pos[1] < self.board_size - 1:
                new_pos[1] += 1

            # Check collision
            if self.board[new_pos[0]][new_pos[1]] != '#':
                # Check for collectible
                if self.board[new_pos[0]][new_pos[1]] == '*':
                    self.score += 10
                    self.board[new_pos[0]][new_pos[1]] = ' '

                self.player_pos = new_pos
            else:
                print("Can't move there - obstacle!")

        except KeyboardInterrupt:
            self.running = False

def main():
    print("Simple Console Game")
    print("Collect all the stars (*) and avoid obstacles (#)")

    game = Game()

    # Show initial state
    game.render()
    print("\nGame demo - showing initial state")
    print("In a real game, this would be interactive")

    # Simulate a few moves
    print("\nSimulating some moves...")
    game.player_pos = [1, 1]  # Move to first star
    game.score += 10
    game.board[1][1] = ' '
    print("Collected first star!")

    game.player_pos = [8, 2]  # Move to second star
    game.score += 10
    game.board[8][2] = ' '
    print("Collected second star!")

    game.render()
    print(f"\nGame Over! Final Score: {game.score}")

if __name__ == "__main__":
    main()
