import urllib.request
import urllib.error
import re
import json

class WebScraper:
    """Simple web scraper using standard library."""

    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

    def fetch_url(self, url):
        """Fetch content from URL."""
        try:
            request = urllib.request.Request(url, headers=self.headers)
            with urllib.request.urlopen(request, timeout=10) as response:
                return response.read().decode('utf-8')
        except urllib.error.URLError as e:
            print(f"Error fetching {url}: {e}")
            return None

    def parse_content(self, html_content):
        """Parse HTML content."""
        if not html_content:
            return {}

        # Extract titles
        titles = re.findall(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)

        # Extract links - FIXED REGEX
        links = re.findall(r'<a[^>]+href=["\']([^"\'>]+)["\'][^>]*>([^<]+)</a>', html_content, re.IGNORECASE)

        # Extract headings
        headings = re.findall(r'<h[1-6][^>]*>([^<]+)</h[1-6]>', html_content, re.IGNORECASE)

        return {
            'titles': titles,
            'links': links,
            'headings': headings,
            'content_length': len(html_content)
        }

def main():
    print("Web Scraping Demo")
    print("=" * 30)

    scraper = WebScraper()

    # Test URLs
    urls = [
        "https://httpbin.org/html",
        "https://example.com"
    ]

    all_data = []
    for url in urls:
        print(f"Scraping: {url}")
        content = scraper.fetch_url(url)
        if content:
            parsed = scraper.parse_content(content)
            all_data.append({"url": url, "data": parsed})
            print(f"  Found {len(parsed.get('titles', []))} titles")

    # Save results
    try:
        with open("scraped_results.json", 'w', encoding='utf-8') as f:
            json.dump(all_data, f, indent=2, ensure_ascii=False)
        print(f"Data saved to scraped_results.json")
    except Exception as e:
        print(f"Error saving data: {e}")

    print(f"\nScraping completed! Processed {len(all_data)} URLs")

if __name__ == "__main__":
    main()
