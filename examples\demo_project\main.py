"""
Simple calculator application with division function.
"""

def add(a, b):
    """Add two numbers."""
    return a + b

def subtract(a, b):
    """Subtract b from a."""
    return a - b

def multiply(a, b):
    """Multiply two numbers."""
    return a * b

def divide(a, b):
    """Divide a by b."""
    try:
        result = a / b
    except ZeroDivisionError:
        print("Cannot divide by zero!")
        return None
    else:
        return result

def modulo(a, b):
    """Return the remainder of dividing a by b."""
    try:
        result = a % b
    except ZeroDivisionError:
        print("Cannot divide by zero!")
        return None
    else:
        return result

def power(a, b):
    """Raise a to the power of b."""
    try:
        result = a ** b
    except ValueError:
        print("Invalid input!")
        return None
    else:
        return result

def main():
    """Main function."""
    print("Simple Calculator")
    print("1. Add")
    print("2. Subtract") 
    print("3. Multiply")
    print("4. Divide")
    print("5. Mo<PERSON>lo")
    print("6. Power")
    
    choice = input("Enter choice (1/2/3/4/5/6): ")
    
    if choice in ['1', '2', '3', '4', '5', '6']:
        num1 = float(input("Enter first number: "))
        num2 = float(input("Enter second number: "))
        
        if choice == '1':
            result = add(num1, num2)
            print(f"{num1} + {num2} = {result}")
        elif choice == '2':
            result = subtract(num1, num2)
            print(f"{num1} - {num2} = {result}")
        elif choice == '3':
            result = multiply(num1, num2)
            print(f"{num1} * {num2} = {result}")
        elif choice == '4':
            result = divide(num1, num2)
            if result is not None:
                print(f"{num1} / {num2} = {result}")
        elif choice == '5':
            result = modulo(num1, num2)
            if result is not None:
                print(f"{num1} % {num2} = {result}")
        elif choice == '6':
            result = power(num1, num2)
            if result is not None:
                print(f"{num1} ** {num2} = {result}")
    else:
        print("Invalid choice!")

if __name__ == "__main__":
    main()