"""
Complete Neural Network implementation from scratch with training and evaluation.
"""

import numpy as np
import matplotlib.pyplot as plt
from data_utils import generate_sample_data, split_data, normalize_data


class NeuralNetwork:
    """A complete neural network implementation from scratch."""
    
    def __init__(self, input_size, hidden_size, output_size, learning_rate=0.01):
        """Initialize the neural network with random weights."""
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size
        self.learning_rate = learning_rate
        
        # Initialize weights and biases
        self.W1 = np.random.randn(input_size, hidden_size) * 0.1
        self.b1 = np.zeros((1, hidden_size))
        self.W2 = np.random.randn(hidden_size, output_size) * 0.1
        self.b2 = np.zeros((1, output_size))
        
        # Store training history
        self.loss_history = []
        self.accuracy_history = []
    
    def sigmoid(self, x):
        """Sigmoid activation function."""
        # Clip x to prevent overflow
        x = np.clip(x, -500, 500)
        return 1 / (1 + np.exp(-x))
    
    def sigmoid_derivative(self, x):
        """Derivative of sigmoid function."""
        return x * (1 - x)
    
    def forward(self, X):
        """Forward propagation."""
        self.z1 = np.dot(X, self.W1) + self.b1
        self.a1 = self.sigmoid(self.z1)
        self.z2 = np.dot(self.a1, self.W2) + self.b2
        self.a2 = self.sigmoid(self.z2)
        return self.a2
    
    def compute_loss(self, y_true, y_pred):
        """Compute binary cross-entropy loss."""
        # Clip predictions to prevent log(0)
        y_pred = np.clip(y_pred, 1e-15, 1 - 1e-15)
        return -np.mean(y_true * np.log(y_pred) + (1 - y_true) * np.log(1 - y_pred))
    
    def backward(self, X, y_true, y_pred):
        """Backward propagation."""
        m = X.shape[0]
        
        # Output layer gradients
        dz2 = y_pred - y_true
        dW2 = (1/m) * np.dot(self.a1.T, dz2)
        db2 = (1/m) * np.sum(dz2, axis=0, keepdims=True)
        
        # Hidden layer gradients
        dz1 = np.dot(dz2, self.W2.T) * self.sigmoid_derivative(self.a1)
        dW1 = (1/m) * np.dot(X.T, dz1)
        db1 = (1/m) * np.sum(dz1, axis=0, keepdims=True)
        
        # Update weights and biases
        self.W2 -= self.learning_rate * dW2
        self.b2 -= self.learning_rate * db2
        self.W1 -= self.learning_rate * dW1
        self.b1 -= self.learning_rate * db1
    
    def train(self, X_train, y_train, X_test, y_test, epochs=1000, print_every=100):
        """Train the neural network."""
        print(f"Training neural network for {epochs} epochs...")
        print(f"Training data shape: {X_train.shape}")
        print(f"Test data shape: {X_test.shape}")
        print("-" * 50)
        
        for epoch in range(epochs):
            # Forward propagation
            y_pred_train = self.forward(X_train)
            
            # Compute loss
            loss = self.compute_loss(y_train.reshape(-1, 1), y_pred_train)
            self.loss_history.append(loss)
            
            # Backward propagation
            self.backward(X_train, y_train.reshape(-1, 1), y_pred_train)
            
            # Evaluate on test set
            if epoch % print_every == 0:
                y_pred_test = self.predict(X_test)
                train_acc = self.accuracy(y_train, (y_pred_train > 0.5).astype(int).flatten())
                test_acc = self.accuracy(y_test, y_pred_test)
                
                self.accuracy_history.append(test_acc)
                
                print(f"Epoch {epoch:4d} | Loss: {loss:.4f} | Train Acc: {train_acc:.3f} | Test Acc: {test_acc:.3f}")
        
        print("-" * 50)
        print("Training completed!")
    
    def predict(self, X):
        """Make predictions on new data."""
        y_pred = self.forward(X)
        return (y_pred > 0.5).astype(int).flatten()
    
    def accuracy(self, y_true, y_pred):
        """Calculate accuracy."""
        return np.mean(y_true == y_pred)
    
    def plot_training_history(self):
        """Plot training loss and accuracy."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Plot loss
        ax1.plot(self.loss_history)
        ax1.set_title('Training Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.grid(True)
        
        # Plot accuracy (sampled every print_every epochs)
        epochs_sampled = range(0, len(self.loss_history), 100)
        ax2.plot(epochs_sampled, self.accuracy_history)
        ax2.set_title('Test Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.grid(True)
        
        plt.tight_layout()
        plt.show()


def main():
    """Main training function."""
    print("🧠 Neural Network Training from Scratch")
    print("=" * 50)
    
    # Generate sample data
    print("📊 Generating sample data...")
    X, y = generate_sample_data(n_samples=1000, n_features=20)
    
    # Split data
    print("🔄 Splitting data...")
    X_train, X_test, y_train, y_test = split_data(X, y, train_ratio=0.8)
    
    # Normalize data
    print("📏 Normalizing data...")
    X_train_norm, X_test_norm = normalize_data(X_train, X_test)
    
    # Create and train neural network
    print("🏗️  Creating neural network...")
    nn = NeuralNetwork(
        input_size=20,
        hidden_size=10,
        output_size=1,
        learning_rate=0.1
    )
    
    # Train the network
    print("🚀 Starting training...")
    nn.train(X_train_norm, y_train, X_test_norm, y_test, epochs=1000, print_every=100)
    
    # Final evaluation
    print("\n📈 Final Evaluation:")
    train_pred = nn.predict(X_train_norm)
    test_pred = nn.predict(X_test_norm)
    
    train_accuracy = nn.accuracy(y_train, train_pred)
    test_accuracy = nn.accuracy(y_test, test_pred)
    
    print(f"Final Training Accuracy: {train_accuracy:.3f}")
    print(f"Final Test Accuracy: {test_accuracy:.3f}")
    
    # Show some predictions
    print("\n🔍 Sample Predictions:")
    for i in range(5):
        actual = y_test[i]
        predicted = test_pred[i]
        confidence = nn.forward(X_test_norm[i:i+1])[0, 0]
        print(f"Sample {i+1}: Actual={actual}, Predicted={predicted}, Confidence={confidence:.3f}")
    
    print("\n✅ Neural network training completed successfully!")


if __name__ == "__main__":
    main()
