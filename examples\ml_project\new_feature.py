import numpy as np
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

class NeuralNetwork(object):
    """A simple neural network for binary classification."""

    def __init__(self, n_features, n_hidden, n_outputs):
        self.n_features = n_features
        self.n_hidden = n_hidden
        self.n_outputs = n_outputs

        # Initialize weights and biases for the network
        self.weights = np.random.rand(self.n_hidden, self.n_features)
        self.bias = np.zeros((self.n_hidden, 1))
        self.output_weights = np.random.rand(self.n_outputs, self.n_hidden)
        self.output_bias = np.zeros((self.n_outputs, 1))

    def forward_pass(self, X):
        """Perform a forward pass through the network."""
        # Calculate the output of the hidden layer
        hidden_layer = np.dot(X, self.weights) + self.bias
        hidden_layer = sigmoid(hidden_layer)

        # Calculate the output of the output layer
        output_layer = np.dot(hidden_layer, self.output_weights) + self.output_bias
        output_layer = sigmoid(output_layer)

        return output_layer

    def backward_pass(self, X, y):
        """Perform a backward pass through the network."""
        # Calculate the error gradient for the output layer
        output_error_gradient = np.zeros((self.n_outputs, 1))
        output_error_gradient[np.arange(self.n_outputs), y] -= 1
        output_error_gradient /= self.n_outputs

        # Calculate the error gradient for the hidden layer
        hidden_error_gradient = np.dot(output_error_gradient, self.output_weights.T) * sigmoid_derivative(self.forward_pass(X))

        # Update the weights and biases for the output layer
        self.output_weights -= 0.1 * np.dot(hidden_layer.T, output_error_gradient)
        self.output_bias -= 0.1 * np.sum(output_error_gradient, axis=0, keepdims=True)

        # Update the weights and biases for the hidden layer
        self.weights -= 0.1 * np.dot(X.T, hidden_error_gradient)
        self.bias -= 0.1 * np.sum(hidden_error_gradient, axis=0, keepdims=True)

    def train(self, X, y):
        """Train the network on a given dataset."""
        for i in range(1000):
            self.forward_pass(X)
            self.backward_pass(X, y)

def sigmoid(x):
    """The sigmoid activation function."""
    return 1 / (1 + np.exp(-x))

def sigmoid_derivative(x):
    """The derivative of the sigmoid activation function."""
    return x * (1 - x)

# Example usage:
X, y = generate_sample_data()
nn = NeuralNetwork(n_features=20, n_hidden=5, n_outputs=2)
nn.train(X, y)