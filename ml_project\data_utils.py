"""
Data utilities for loading and preprocessing data.
"""

import numpy as np

def generate_sample_data(n_samples=1000, n_features=20):
    """Generate sample classification data."""
    np.random.seed(42)
    X = np.random.randn(n_samples, n_features)
    # Create a simple linear relationship for classification
    weights = np.random.randn(n_features)
    y = (X @ weights > 0).astype(int)
    return X, y

def split_data(X, y, train_ratio=0.8):
    """Split data into train and test sets."""
    n_train = int(len(X) * train_ratio)
    indices = np.random.permutation(len(X))
    
    train_idx = indices[:n_train]
    test_idx = indices[n_train:]
    
    return X[train_idx], X[test_idx], y[train_idx], y[test_idx]

def normalize_data(X_train, X_test):
    """Normalize features to zero mean and unit variance."""
    mean = np.mean(X_train, axis=0)
    std = np.std(X_train, axis=0)
    
    X_train_norm = (X_train - mean) / (std + 1e-8)
    X_test_norm = (X_test - mean) / (std + 1e-8)
    
    return X_train_norm, X_test_norm
