# utils.py
import numpy as np
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

class NeuralNetwork(object):
    """A simple neural network for binary classification."""

    def __init__(self, n_features, n_hidden, n_outputs):
        self.n_features = n_features
        self.n_hidden = n_hidden
        self.n_outputs = n_outputs

        # Initialize weights and biases for the network
        self.weights = np.random.rand(self.n_hidden, self.n_features)
        self.bias = np.zeros((self.n_hidden, 1))

    def train(self, X, y):
        """Train the neural network on a given dataset."""
        # Split the data into training and test sets
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)

        # Train the network using the training set
        self.weights = np.random.rand(self.n_hidden, self.n_features)
        self.bias = np.zeros((self.n_hidden, 1))

        for _ in range(100):
            # Forward pass
            hidden_layer = X_train @ self.weights + self.bias
            output_layer = sigmoid(hidden_layer)

            # Backward pass
            error = y_train - output_layer
            gradient = np.dot(X_train.T, error) / len(X_train)
            self.weights -= 0.01 * gradient
            self.bias -= 0.01 * np.mean(error, axis=0, keepdims=True)

        # Evaluate the network on the test set
        y_pred = self.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        print("Accuracy:", accuracy)
        print("Confusion matrix:\n", confusion_matrix(y_test, y_pred))
        print("Classification report:\n", classification_report(y_test, y_pred))

    def predict(self, X):
        """Make predictions on a given dataset."""
        hidden_layer = X @ self.weights + self.bias
        output_layer = sigmoid(hidden_layer)
        return np.round(output_layer).astype(int)