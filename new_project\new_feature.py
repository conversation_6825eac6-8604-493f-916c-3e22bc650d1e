import numpy as np
from tensorflow.keras import models, layers

class NewFeature(models.Model):
    """A simple neural network for training on any dataset."""

    def __init__(self, input_shape, output_shape):
        super().__init__()
        self.input_shape = input_shape
        self.output_shape = output_shape

        # Define the layers of the neural network
        self.layers = [
            layers.Dense(64, activation='relu', input_shape=input_shape),
            layers.Dense(32, activation='relu'),
            layers.Dense(output_shape)
        ]

    def call(self, inputs):
        """Forward pass of the neural network."""
        for layer in self.layers:
            inputs = layer(inputs)
        return inputs

    def compile(self, optimizer='adam', loss='mean_squared_error'):
        """Compile the model with an optimizer and a loss function."""
        self.optimizer = optimizer
        self.loss = loss

    def fit(self, X, y, epochs=10, batch_size=32):
        """Train the model on a dataset."""
        # Split the data into training and validation sets
        X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2)

        # Compile the model with an optimizer and a loss function
        self.compile()

        # Train the model on the training set
        history = self.fit(X_train, y_train, epochs=epochs, batch_size=batch_size)

        # Evaluate the model on the validation set
        val_loss = self.evaluate(X_val, y_val)

        return history, val_loss