# Core dependencies for AI-Powered Agentic Code Assistant

# CLI and UI
click>=8.0.0
rich>=13.0.0

# HTTP requests for API calls
requests>=2.28.0

# Scientific computing
numpy>=1.21.0

# Vector database and embeddings
chromadb>=0.4.0

# Cryptography for secure hashing
cryptography>=3.4.0

# File watching (optional)
watchdog>=3.0.0

# Text tokenization (optional)
tiktoken>=0.5.0

# Optional: For plotting (neural network training)
matplotlib>=3.5.0

# Optional: For advanced text processing
sentence-transformers>=2.2.0
