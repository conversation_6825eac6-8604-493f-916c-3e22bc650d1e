"""
Simple Neural Network Demo - Generated by Intelligent Augment Engine
This demonstrates what the engine should produce for complex tasks.
"""

import random
import math

def create_dataset():
    """Create sample binary classification dataset."""
    # Generate synthetic data
    data = []
    labels = []
    
    for _ in range(1000):
        # Generate 2D points
        x1 = random.uniform(-2, 2)
        x2 = random.uniform(-2, 2)
        
        # Simple classification rule: points inside/outside a circle
        label = 1 if x1**2 + x2**2 < 1.5 else 0
        
        data.append([x1, x2])
        labels.append(label)
    
    return data, labels

class NeuralNetwork:
    """Simple neural network implementation using only standard library."""
    
    def __init__(self, input_size=2, hidden_size=10, output_size=1):
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size
        
        # Initialize weights randomly
        self.weights1 = [[random.uniform(-1, 1) for _ in range(hidden_size)] for _ in range(input_size)]
        self.bias1 = [random.uniform(-1, 1) for _ in range(hidden_size)]
        
        self.weights2 = [[random.uniform(-1, 1) for _ in range(output_size)] for _ in range(hidden_size)]
        self.bias2 = [random.uniform(-1, 1) for _ in range(output_size)]
        
        self.learning_rate = 0.1
    
    def sigmoid(self, x):
        """Sigmoid activation function."""
        return 1 / (1 + math.exp(-max(-500, min(500, x))))  # Prevent overflow
    
    def forward(self, inputs):
        """Forward pass through the network."""
        # Hidden layer
        hidden = []
        for i in range(self.hidden_size):
            weighted_sum = sum(inputs[j] * self.weights1[j][i] for j in range(self.input_size))
            hidden.append(self.sigmoid(weighted_sum + self.bias1[i]))
        
        # Output layer
        output = []
        for i in range(self.output_size):
            weighted_sum = sum(hidden[j] * self.weights2[j][i] for j in range(self.hidden_size))
            output.append(self.sigmoid(weighted_sum + self.bias2[i]))
        
        return hidden, output
    
    def train_step(self, inputs, target):
        """Single training step with backpropagation."""
        # Forward pass
        hidden, output = self.forward(inputs)
        
        # Calculate error
        output_error = target - output[0]
        
        # Backpropagation (simplified)
        # Update output weights
        for i in range(self.hidden_size):
            self.weights2[i][0] += self.learning_rate * output_error * hidden[i]
        self.bias2[0] += self.learning_rate * output_error
        
        # Update hidden weights (simplified)
        for i in range(self.input_size):
            for j in range(self.hidden_size):
                hidden_error = output_error * self.weights2[j][0] * hidden[j] * (1 - hidden[j])
                self.weights1[i][j] += self.learning_rate * hidden_error * inputs[i]
        
        return abs(output_error)

def train_model(model, data, labels, epochs=50):
    """Train the neural network with progress bar."""
    print(f"Training neural network for {epochs} epochs...")
    
    for epoch in range(epochs):
        total_error = 0
        
        # Train on all samples
        for i in range(len(data)):
            error = model.train_step(data[i], labels[i])
            total_error += error
        
        # Show progress bar
        if epoch % 5 == 0:
            avg_error = total_error / len(data)
            progress = "█" * (epoch // 5) + "░" * (10 - epoch // 5)
            percent = (epoch / epochs) * 100
            print(f"Epoch {epoch:2d}/{epochs} [{progress}] {percent:5.1f}% Error: {avg_error:.4f}")
    
    print("Training completed!")
    return model

def evaluate_model(model, data, labels):
    """Evaluate model accuracy."""
    correct = 0
    total = len(data)
    
    for i in range(total):
        _, output = model.forward(data[i])
        prediction = 1 if output[0] > 0.5 else 0
        if prediction == labels[i]:
            correct += 1
    
    accuracy = correct / total
    print(f"Model accuracy: {accuracy:.2%} ({correct}/{total})")
    return accuracy

if __name__ == "__main__":
    print("🧠 Neural Network Training Demo")
    print("=" * 40)
    
    # Create dataset
    data, labels = create_dataset()
    print(f"Created dataset with {len(data)} samples")
    
    # Create and train model
    model = NeuralNetwork(input_size=2, hidden_size=10, output_size=1)
    trained_model = train_model(model, data, labels, epochs=50)
    
    # Evaluate model
    accuracy = evaluate_model(trained_model, data, labels)
    
    print(f"\n🎉 Training completed! Final accuracy: {accuracy:.2%}")
    print("\nThis demonstrates:")
    print("✅ Complete neural network implementation")
    print("✅ Training with progress bar")
    print("✅ Binary classification")
    print("✅ Using only standard library")
    print("✅ Self-contained, working code")
