#!/usr/bin/env python3
"""Simple test to check if Python is working."""

print("🐍 Python is working!")
print("✅ Basic functionality test passed")

# Test basic operations
result = 2 + 2
print(f"✅ Math works: 2 + 2 = {result}")

# Test imports
try:
    import os
    print("✅ os module imported")
except Exception as e:
    print(f"❌ os import failed: {e}")

try:
    from pathlib import Path
    print("✅ pathlib imported")
except Exception as e:
    print(f"❌ pathlib import failed: {e}")

print("🎉 Simple test completed!")
