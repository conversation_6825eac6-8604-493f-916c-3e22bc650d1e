"""
STRESS TEST: The Actual Augment Engine
Test the FinalWorkingEngine under extreme conditions to find weaknesses.
"""

import time
import traceback
from pathlib import Path
from core.final_working_engine import FinalWorkingEngine

def test_basic_engine_functionality():
    """Test basic engine operations."""
    print("TEST 1: Basic Engine Functionality")
    print("=" * 50)
    
    engine = FinalWorkingEngine()
    test_dir = Path("./stress_test_output")
    test_dir.mkdir(exist_ok=True)
    
    # Test simple task
    try:
        result = engine.augment_code("create a simple hello world function", test_dir)
        
        if result.success:
            print("✅ PASS: Basic task execution")
            print(f"   Time: {result.execution_time:.2f}s")
            print(f"   Iterations: {result.iterations_used}")
        else:
            print("❌ FAIL: Basic task failed")
            print(f"   Error: {result.error}")
            
        return result.success
        
    except Exception as e:
        print(f"❌ CRASH: Engine crashed on basic task: {e}")
        return False

def test_complex_tasks():
    """Test all complex task types."""
    print("\nTEST 2: Complex Task Types")
    print("=" * 50)
    
    engine = FinalWorkingEngine()
    test_dir = Path("./stress_test_output")
    
    complex_tasks = [
        ("Neural Network", "create a neural network for binary classification with training progress"),
        ("Web Scraper", "build a web scraper that downloads and parses HTML content"),
        ("Data Analysis", "create a data analyzer that processes CSV files and calculates statistics"),
        ("Game Development", "make a console game with player movement and collision detection"),
        ("File Processing", "create a file processor that reads, analyzes, and transforms text files"),
        ("Algorithm Implementation", "implement a sorting algorithm with performance comparison"),
        ("Database Simulation", "create a simple in-memory database with CRUD operations"),
        ("Network Tool", "build a network utility that pings hosts and reports connectivity")
    ]
    
    results = []
    
    for task_name, prompt in complex_tasks:
        print(f"\nTesting: {task_name}")
        print("-" * 30)
        
        try:
            start_time = time.time()
            result = engine.augment_code(prompt, test_dir)
            end_time = time.time()
            
            if result.success:
                print(f"✅ PASS: {task_name}")
                print(f"   Time: {result.execution_time:.2f}s")
                print(f"   Output length: {len(result.output)} chars")
                results.append(True)
            else:
                print(f"❌ FAIL: {task_name}")
                print(f"   Error: {result.error[:100]}...")
                results.append(False)
                
        except Exception as e:
            print(f"❌ CRASH: {task_name} crashed: {e}")
            results.append(False)
    
    success_rate = (sum(results) / len(results)) * 100
    print(f"\nComplex Tasks Success Rate: {sum(results)}/{len(results)} ({success_rate:.1f}%)")
    
    return success_rate >= 75  # 75% success rate required

def test_edge_cases():
    """Test edge cases and unusual inputs."""
    print("\nTEST 3: Edge Cases and Unusual Inputs")
    print("=" * 50)
    
    engine = FinalWorkingEngine()
    test_dir = Path("./stress_test_output")
    
    edge_cases = [
        ("Empty prompt", ""),
        ("Very short prompt", "hi"),
        ("Very long prompt", "create a " + "very " * 100 + "complex system"),
        ("Special characters", "create a function with @#$%^&*() symbols"),
        ("Multiple tasks", "create a neural network and also a web scraper and a game"),
        ("Contradictory request", "create a simple complex easy difficult function"),
        ("Non-programming request", "write a poem about coding"),
        ("Impossible request", "create a function that solves the halting problem"),
        ("Vague request", "make something cool"),
        ("Technical jargon", "implement a convolutional autoencoder with batch normalization")
    ]
    
    results = []
    
    for case_name, prompt in edge_cases:
        print(f"\nTesting: {case_name}")
        
        try:
            result = engine.augment_code(prompt, test_dir)
            
            if result.success:
                print(f"✅ HANDLED: {case_name}")
                results.append(True)
            else:
                print(f"⚠️  FAILED: {case_name}")
                print(f"   Error: {result.error[:50]}...")
                results.append(False)
                
        except Exception as e:
            print(f"❌ CRASH: {case_name} crashed: {e}")
            results.append(False)
    
    success_rate = (sum(results) / len(results)) * 100
    print(f"\nEdge Cases Handled: {sum(results)}/{len(results)} ({success_rate:.1f}%)")
    
    return success_rate >= 50  # 50% success rate for edge cases

def test_performance_under_load():
    """Test engine performance under repeated load."""
    print("\nTEST 4: Performance Under Load")
    print("=" * 50)
    
    engine = FinalWorkingEngine()
    test_dir = Path("./stress_test_output")
    
    # Run the same task multiple times quickly
    task = "create a simple calculator function"
    num_runs = 5
    
    times = []
    successes = 0
    
    print(f"Running '{task}' {num_runs} times...")
    
    for i in range(num_runs):
        try:
            start_time = time.time()
            result = engine.augment_code(task, test_dir)
            end_time = time.time()
            
            execution_time = end_time - start_time
            times.append(execution_time)
            
            if result.success:
                successes += 1
                print(f"  Run {i+1}: ✅ {execution_time:.2f}s")
            else:
                print(f"  Run {i+1}: ❌ {execution_time:.2f}s - {result.error[:30]}...")
                
        except Exception as e:
            print(f"  Run {i+1}: 💥 CRASH - {e}")
    
    if times:
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"\nPerformance Results:")
        print(f"   Success rate: {successes}/{num_runs} ({successes/num_runs*100:.1f}%)")
        print(f"   Average time: {avg_time:.2f}s")
        print(f"   Time range: {min_time:.2f}s - {max_time:.2f}s")
        
        # Check for performance degradation
        if max_time > avg_time * 2:
            print("⚠️  WARNING: Significant performance variation detected")
        else:
            print("✅ PASS: Consistent performance")
            
        return successes >= num_runs * 0.8  # 80% success rate
    else:
        print("❌ FAIL: No successful runs")
        return False

def test_error_recovery():
    """Test engine's ability to handle and recover from errors."""
    print("\nTEST 5: Error Recovery")
    print("=" * 50)
    
    engine = FinalWorkingEngine()
    test_dir = Path("./stress_test_output")
    
    # Test tasks that might cause issues
    problematic_tasks = [
        "create a function that imports nonexistent_library",
        "make a neural network using tensorflow",  # Not available
        "build a GUI application using tkinter",   # Might not work in all environments
        "create a function with syntax errors intentionally",
        "implement something using undefined_function()"
    ]
    
    recovery_count = 0
    
    for task in problematic_tasks:
        print(f"\nTesting problematic task: {task[:40]}...")
        
        try:
            result = engine.augment_code(task, test_dir)
            
            # Even if it fails, the engine should handle it gracefully
            if result.error and "crash" not in result.error.lower():
                print("✅ GRACEFUL: Engine handled error gracefully")
                recovery_count += 1
            elif result.success:
                print("✅ SOLVED: Engine somehow made it work")
                recovery_count += 1
            else:
                print("⚠️  POOR: Engine failed ungracefully")
                
        except Exception as e:
            print(f"❌ CRASH: Engine crashed: {e}")
    
    recovery_rate = (recovery_count / len(problematic_tasks)) * 100
    print(f"\nError Recovery Rate: {recovery_count}/{len(problematic_tasks)} ({recovery_rate:.1f}%)")
    
    return recovery_rate >= 60  # 60% recovery rate

def run_comprehensive_stress_test():
    """Run all stress tests on the Augment Engine."""
    print("AUGMENT ENGINE COMPREHENSIVE STRESS TEST")
    print("=" * 60)
    print("Testing the FinalWorkingEngine under extreme conditions...")
    print()
    
    tests = [
        ("Basic Functionality", test_basic_engine_functionality),
        ("Complex Tasks", test_complex_tasks),
        ("Edge Cases", test_edge_cases),
        ("Performance Load", test_performance_under_load),
        ("Error Recovery", test_error_recovery)
    ]
    
    results = []
    start_time = time.time()
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Final results
    print("\n" + "=" * 60)
    print("COMPREHENSIVE STRESS TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / len(results)) * 100
    print(f"\nOverall Success Rate: {passed}/{len(results)} ({success_rate:.1f}%)")
    print(f"Total Test Time: {total_time:.2f} seconds")
    
    # Final verdict
    print(f"\n🎯 STRESS TEST VERDICT:")
    if success_rate >= 90:
        print("🏆 EXCELLENT! Engine is extremely robust and production-ready!")
    elif success_rate >= 75:
        print("✅ GOOD! Engine is solid with minor issues.")
    elif success_rate >= 50:
        print("⚠️  MODERATE! Engine works but has significant weaknesses.")
    else:
        print("❌ POOR! Engine fails under stress and needs major improvements.")
    
    print(f"\nStress Test Score: {success_rate:.1f}%")
    
    return success_rate

if __name__ == "__main__":
    print("🚨 STARTING AUGMENT ENGINE STRESS TEST")
    print("This will push the engine to its limits...")
    print()
    
    success_rate = run_comprehensive_stress_test()
    
    print(f"\n" + "=" * 60)
    print("FINAL ASSESSMENT")
    print("=" * 60)
    
    if success_rate >= 75:
        print("🎉 The Augment Engine is ROBUST and ready for real-world use!")
        print("It can handle complex tasks, edge cases, and stress conditions.")
    else:
        print("⚠️  The Augment Engine needs MORE WORK before production use.")
        print("Significant issues were found under stress testing.")
    
    print(f"\nFinal Stress Test Score: {success_rate:.1f}%")
    print("\nStress test completed. Check ./stress_test_output/ for generated files.")
