"""
STRESS TEST: Neural Network Implementation
Test the neural network under various conditions to find weaknesses.
"""

import sys
import time
import subprocess
import tempfile
from pathlib import Path

def test_basic_functionality():
    """Test basic neural network functionality."""
    print("TEST 1: Basic Functionality")
    print("=" * 40)
    
    result = subprocess.run(
        ['python', 'examples/demo_project/final_neural_network.py'],
        capture_output=True,
        text=True,
        timeout=30
    )
    
    if result.returncode == 0:
        print("✅ PASS: Basic execution works")
        
        # Check for key outputs
        output = result.stdout
        if "Training completed!" in output:
            print("✅ PASS: Training completes")
        else:
            print("❌ FAIL: Training doesn't complete")
            
        if "Model accuracy:" in output:
            print("✅ PASS: Accuracy calculation works")
            # Extract accuracy
            import re
            accuracy_match = re.search(r'Model accuracy: (\d+\.\d+)%', output)
            if accuracy_match:
                accuracy = float(accuracy_match.group(1))
                print(f"   Accuracy: {accuracy:.2f}%")
                if accuracy > 70:
                    print("✅ PASS: Accuracy is reasonable (>70%)")
                else:
                    print("⚠️  WARN: Low accuracy (<70%)")
            else:
                print("❌ FAIL: Can't extract accuracy")
        else:
            print("❌ FAIL: No accuracy output")
            
        if "progress" in output.lower() or "[" in output:
            print("✅ PASS: Progress bar works")
        else:
            print("❌ FAIL: No progress bar")
            
    else:
        print("❌ FAIL: Basic execution failed")
        print(f"Error: {result.stderr}")
    
    return result.returncode == 0

def test_multiple_runs():
    """Test consistency across multiple runs."""
    print("\nTEST 2: Multiple Runs Consistency")
    print("=" * 40)
    
    accuracies = []
    times = []
    
    for i in range(3):
        print(f"Run {i+1}/3...")
        start_time = time.time()
        
        result = subprocess.run(
            ['python', 'examples/demo_project/final_neural_network.py'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        times.append(execution_time)
        
        if result.returncode == 0:
            # Extract accuracy
            import re
            accuracy_match = re.search(r'Model accuracy: (\d+\.\d+)%', result.stdout)
            if accuracy_match:
                accuracy = float(accuracy_match.group(1))
                accuracies.append(accuracy)
                print(f"   Accuracy: {accuracy:.2f}%, Time: {execution_time:.2f}s")
            else:
                print("   ❌ Failed to extract accuracy")
        else:
            print(f"   ❌ Run failed: {result.stderr}")
    
    if len(accuracies) == 3:
        avg_accuracy = sum(accuracies) / len(accuracies)
        min_accuracy = min(accuracies)
        max_accuracy = max(accuracies)
        accuracy_std = (sum((x - avg_accuracy)**2 for x in accuracies) / len(accuracies))**0.5
        
        avg_time = sum(times) / len(times)
        
        print(f"\nResults:")
        print(f"   Average accuracy: {avg_accuracy:.2f}%")
        print(f"   Accuracy range: {min_accuracy:.2f}% - {max_accuracy:.2f}%")
        print(f"   Accuracy std dev: {accuracy_std:.2f}%")
        print(f"   Average time: {avg_time:.2f}s")
        
        if accuracy_std < 5.0:
            print("✅ PASS: Consistent results (std dev < 5%)")
        else:
            print("⚠️  WARN: High variance in results")
            
        if avg_accuracy > 75:
            print("✅ PASS: Good average accuracy (>75%)")
        else:
            print("❌ FAIL: Poor average accuracy (<75%)")
            
        return True
    else:
        print("❌ FAIL: Not all runs completed successfully")
        return False

def test_code_structure():
    """Test the actual code structure and implementation."""
    print("\nTEST 3: Code Structure Analysis")
    print("=" * 40)
    
    # Read the neural network code
    with open('examples/demo_project/final_neural_network.py', 'r') as f:
        code = f.read()
    
    # Check for key components
    checks = [
        ("create_dataset function", "def create_dataset"),
        ("NeuralNetwork class", "class NeuralNetwork"),
        ("sigmoid function", "def sigmoid"),
        ("forward function", "def forward"),
        ("train_step function", "def train_step"),
        ("train_model function", "def train_model"),
        ("evaluate_model function", "def evaluate_model"),
        ("main function", "def main"),
        ("progress bar", "progress"),
        ("weights initialization", "weights"),
        ("backpropagation", "output_error"),
        ("learning rate", "learning_rate")
    ]
    
    passed = 0
    for check_name, check_pattern in checks:
        if check_pattern in code:
            print(f"✅ PASS: {check_name} found")
            passed += 1
        else:
            print(f"❌ FAIL: {check_name} missing")
    
    print(f"\nStructure Score: {passed}/{len(checks)} ({passed/len(checks)*100:.1f}%)")
    
    # Check for potential issues
    issues = []
    if "import tensorflow" in code or "import torch" in code:
        issues.append("Uses external ML libraries")
    if "undefined" in code.lower():
        issues.append("Contains undefined references")
    if code.count("def ") < 5:
        issues.append("Too few functions defined")
    
    if issues:
        print("\nPotential Issues:")
        for issue in issues:
            print(f"⚠️  {issue}")
    else:
        print("\n✅ No obvious structural issues found")
    
    return passed >= len(checks) * 0.8  # 80% pass rate

def test_edge_cases():
    """Test edge cases and error handling."""
    print("\nTEST 4: Edge Cases and Error Handling")
    print("=" * 40)
    
    # Test with modified parameters
    test_cases = [
        ("Small dataset", "data, labels = create_dataset(); data = data[:10]; labels = labels[:10]"),
        ("Single epoch", "epochs=1"),
        ("Large learning rate", "model.learning_rate = 1.0"),
        ("Small learning rate", "model.learning_rate = 0.001")
    ]
    
    base_code = open('examples/demo_project/final_neural_network.py', 'r').read()
    
    passed_tests = 0
    
    for test_name, modification in test_cases:
        print(f"Testing: {test_name}")
        
        # Create modified code
        if "epochs=" in modification:
            modified_code = base_code.replace("epochs=50", modification)
        elif "learning_rate" in modification:
            modified_code = base_code.replace("def main():", f"def main():\n    # Test modification\n    pass\n\ndef test_main():")
            modified_code += f"\n\nif __name__ == '__main__':\n    {modification}\n    test_main()"
        else:
            # For dataset modifications, we'll create a simpler test
            modified_code = base_code.replace("data, labels = create_dataset()", modification)
        
        # Write to temp file and test
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(modified_code)
            temp_file = f.name
        
        try:
            result = subprocess.run(
                ['python', temp_file],
                capture_output=True,
                text=True,
                timeout=15
            )
            
            if result.returncode == 0:
                print(f"   ✅ PASS: {test_name} works")
                passed_tests += 1
            else:
                print(f"   ❌ FAIL: {test_name} failed")
                print(f"      Error: {result.stderr[:100]}...")
                
        except subprocess.TimeoutExpired:
            print(f"   ⚠️  TIMEOUT: {test_name} took too long")
        except Exception as e:
            print(f"   ❌ ERROR: {test_name} - {e}")
        finally:
            try:
                Path(temp_file).unlink()
            except:
                pass
    
    print(f"\nEdge Case Score: {passed_tests}/{len(test_cases)} ({passed_tests/len(test_cases)*100:.1f}%)")
    return passed_tests >= len(test_cases) * 0.5  # 50% pass rate for edge cases

def run_stress_test():
    """Run comprehensive stress test."""
    print("NEURAL NETWORK STRESS TEST")
    print("=" * 50)
    print("Testing the generated neural network implementation...")
    print()
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Multiple Runs", test_multiple_runs),
        ("Code Structure", test_code_structure),
        ("Edge Cases", test_edge_cases)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Final results
    print("\n" + "=" * 50)
    print("STRESS TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / len(results)) * 100
    print(f"\nOverall Success Rate: {passed}/{len(results)} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("\n🎉 EXCELLENT! Neural network passes all stress tests!")
        print("The implementation is robust and production-ready.")
    elif success_rate >= 75:
        print("\n👍 GOOD! Neural network passes most stress tests.")
        print("Minor issues but generally solid implementation.")
    elif success_rate >= 50:
        print("\n⚠️  MODERATE! Neural network has some issues.")
        print("Works for basic cases but needs improvement.")
    else:
        print("\n❌ POOR! Neural network fails most stress tests.")
        print("Significant issues that need to be addressed.")
    
    return success_rate

if __name__ == "__main__":
    success_rate = run_stress_test()
    
    print(f"\n🎯 FINAL VERDICT:")
    if success_rate >= 75:
        print("The neural network implementation is SOLID and ready for use!")
    else:
        print("The neural network implementation needs MORE WORK.")
    
    print(f"Stress Test Score: {success_rate:.1f}%")
