"""
TEST 1: Core Configuration Testing
Test all configuration settings and imports.
"""

def test_config_imports():
    """Test that config module imports correctly."""
    print("TEST 1.1: Config Module Import")
    print("=" * 40)
    
    try:
        import core.config as config
        print("✅ PASS: core.config imports successfully")
        return True
    except Exception as e:
        print(f"❌ FAIL: core.config import failed: {e}")
        return False

def test_config_values():
    """Test that all config values are properly set."""
    print("\nTEST 1.2: Config Values")
    print("=" * 40)
    
    try:
        import core.config as config
        
        # Test required attributes exist
        required_attrs = [
            'DB_PATH', 'EMBEDDINGS_MODEL', 'SUPPORTED_EXTENSIONS',
            'MIN_CHUNK_SIZE', 'MAX_CHUNK_SIZE', 'ENCRYPTION_KEY_FILE',
            'EMBEDDING_DIMENSION', 'BATCH_SIZE', 'IGNORE_PATTERNS'
        ]
        
        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(config, attr):
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"❌ FAIL: Missing attributes: {missing_attrs}")
            return False
        
        print("✅ PASS: All required config attributes exist")
        
        # Test value types
        checks = [
            ('DB_PATH', 'Path object', hasattr(config.DB_PATH, 'exists')),
            ('EMBEDDINGS_MODEL', 'string', isinstance(config.EMBEDDINGS_MODEL, str)),
            ('SUPPORTED_EXTENSIONS', 'set', isinstance(config.SUPPORTED_EXTENSIONS, set)),
            ('MIN_CHUNK_SIZE', 'int', isinstance(config.MIN_CHUNK_SIZE, int)),
            ('MAX_CHUNK_SIZE', 'int', isinstance(config.MAX_CHUNK_SIZE, int)),
            ('EMBEDDING_DIMENSION', 'int', isinstance(config.EMBEDDING_DIMENSION, int)),
            ('BATCH_SIZE', 'int', isinstance(config.BATCH_SIZE, int)),
            ('IGNORE_PATTERNS', 'set', isinstance(config.IGNORE_PATTERNS, set))
        ]
        
        for attr_name, expected_type, is_correct in checks:
            if is_correct:
                print(f"✅ PASS: {attr_name} is {expected_type}")
            else:
                print(f"❌ FAIL: {attr_name} is not {expected_type}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Config values test failed: {e}")
        return False

def test_config_logic():
    """Test logical consistency of config values."""
    print("\nTEST 1.3: Config Logic")
    print("=" * 40)
    
    try:
        import core.config as config
        
        # Test chunk size logic
        if config.MIN_CHUNK_SIZE >= config.MAX_CHUNK_SIZE:
            print("❌ FAIL: MIN_CHUNK_SIZE should be less than MAX_CHUNK_SIZE")
            return False
        print("✅ PASS: Chunk size logic is correct")
        
        # Test supported extensions format
        for ext in config.SUPPORTED_EXTENSIONS:
            if not ext.startswith('.'):
                print(f"❌ FAIL: Extension '{ext}' should start with '.'")
                return False
        print("✅ PASS: All extensions start with '.'")
        
        # Test ignore patterns are not empty
        if not config.IGNORE_PATTERNS:
            print("❌ FAIL: IGNORE_PATTERNS should not be empty")
            return False
        print("✅ PASS: IGNORE_PATTERNS is not empty")
        
        # Test embedding dimension is reasonable
        if config.EMBEDDING_DIMENSION <= 0:
            print("❌ FAIL: EMBEDDING_DIMENSION should be positive")
            return False
        print("✅ PASS: EMBEDDING_DIMENSION is positive")
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Config logic test failed: {e}")
        return False

def test_config_dependencies():
    """Test that config dependencies are available."""
    print("\nTEST 1.4: Config Dependencies")
    print("=" * 40)
    
    try:
        # Test pathlib import
        from pathlib import Path
        print("✅ PASS: pathlib.Path is available")
        
        # Test os import
        import os
        print("✅ PASS: os module is available")
        
        # Test that Path works as expected
        test_path = Path("./test")
        if hasattr(test_path, 'exists'):
            print("✅ PASS: Path object has expected methods")
        else:
            print("❌ FAIL: Path object missing expected methods")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Config dependencies test failed: {e}")
        return False

def run_config_tests():
    """Run all configuration tests."""
    print("CONFIGURATION TESTING")
    print("=" * 50)
    print("Testing core/config.py functionality...")
    print()
    
    tests = [
        ("Config Import", test_config_imports),
        ("Config Values", test_config_values),
        ("Config Logic", test_config_logic),
        ("Config Dependencies", test_config_dependencies)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("CONFIG TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / len(results)) * 100
    print(f"\nConfig Success Rate: {passed}/{len(results)} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("\n🎉 EXCELLENT! Config module is working perfectly!")
        print("Ready to move to next component: File Operations")
    elif success_rate >= 75:
        print("\n👍 GOOD! Config module mostly works with minor issues.")
    else:
        print("\n❌ POOR! Config module has significant issues that need fixing.")
    
    return success_rate

if __name__ == "__main__":
    success_rate = run_config_tests()
    
    print(f"\n🎯 CONFIG TEST VERDICT:")
    if success_rate >= 90:
        print("Config module is SOLID and ready for use!")
    else:
        print("Config module needs FIXES before proceeding.")
    
    print(f"Test Score: {success_rate:.1f}%")
