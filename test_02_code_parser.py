"""
TEST 2: Code Parser Testing
Test code parsing functionality for extracting code chunks.
"""

import tempfile
from pathlib import Path

def test_parser_imports():
    """Test that code parser imports correctly."""
    print("TEST 2.1: Code Parser Import")
    print("=" * 40)
    
    try:
        from core.code_parser import <PERSON><PERSON>hunk, PythonParser, UniversalParser
        print("✅ PASS: code_parser imports successfully")
        print("✅ PASS: CodeChunk class available")
        print("✅ PASS: PythonParser class available") 
        print("✅ PASS: UniversalParser class available")
        return True
    except Exception as e:
        print(f"❌ FAIL: code_parser import failed: {e}")
        return False

def test_code_chunk_creation():
    """Test CodeChunk dataclass creation."""
    print("\nTEST 2.2: CodeChunk Creation")
    print("=" * 40)
    
    try:
        from core.code_parser import CodeChunk
        
        # Test basic creation
        chunk = CodeChunk(
            content="def test(): pass",
            start_line=1,
            end_line=1,
            chunk_type="function",
            name="test"
        )
        
        print("✅ PASS: Basic CodeChunk creation works")
        
        # Test attributes
        if chunk.content == "def test(): pass":
            print("✅ PASS: content attribute works")
        else:
            print("❌ FAIL: content attribute incorrect")
            return False
            
        if chunk.chunk_type == "function":
            print("✅ PASS: chunk_type attribute works")
        else:
            print("❌ FAIL: chunk_type attribute incorrect")
            return False
            
        # Test optional attributes
        if chunk.parent is None:
            print("✅ PASS: optional parent attribute works")
        else:
            print("❌ FAIL: optional parent attribute incorrect")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: CodeChunk creation failed: {e}")
        return False

def test_python_parser_basic():
    """Test basic Python parser functionality."""
    print("\nTEST 2.3: Python Parser Basic")
    print("=" * 40)
    
    try:
        from core.code_parser import PythonParser
        
        # Create a simple Python file for testing
        test_code = '''def hello_world():
    """A simple hello world function."""
    print("Hello, World!")
    return "Hello"

class TestClass:
    """A test class."""
    
    def __init__(self):
        self.value = 42
    
    def get_value(self):
        """Get the value."""
        return self.value

import os
from pathlib import Path
'''
        
        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(test_code)
            temp_file = Path(f.name)
        
        try:
            parser = PythonParser()
            chunks = parser.parse_file(temp_file)
            
            print(f"✅ PASS: Parser created and executed (found {len(chunks)} chunks)")
            
            # Check if we found expected chunks
            chunk_types = [chunk.chunk_type for chunk in chunks]
            chunk_names = [chunk.name for chunk in chunks]
            
            if 'function' in chunk_types:
                print("✅ PASS: Found function chunks")
            else:
                print("❌ FAIL: No function chunks found")
                return False
                
            if 'class' in chunk_types:
                print("✅ PASS: Found class chunks")
            else:
                print("❌ FAIL: No class chunks found")
                return False
                
            if 'import' in chunk_types:
                print("✅ PASS: Found import chunks")
            else:
                print("❌ FAIL: No import chunks found")
                return False
            
            # Check specific names
            if 'hello_world' in chunk_names:
                print("✅ PASS: Found hello_world function")
            else:
                print("❌ FAIL: hello_world function not found")
                return False
                
            if 'TestClass' in chunk_names:
                print("✅ PASS: Found TestClass class")
            else:
                print("❌ FAIL: TestClass class not found")
                return False
            
            return True
            
        finally:
            # Clean up
            temp_file.unlink()
        
    except Exception as e:
        print(f"❌ FAIL: Python parser test failed: {e}")
        return False

def test_universal_parser():
    """Test universal parser functionality."""
    print("\nTEST 2.4: Universal Parser")
    print("=" * 40)
    
    try:
        from core.code_parser import UniversalParser
        
        parser = UniversalParser()
        print("✅ PASS: UniversalParser created successfully")
        
        # Test Python file parsing
        test_code = 'def test(): pass'
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(test_code)
            temp_py_file = Path(f.name)
        
        try:
            chunks = parser.parse_file(temp_py_file)
            if chunks:
                print("✅ PASS: Universal parser handles .py files")
            else:
                print("❌ FAIL: Universal parser failed on .py files")
                return False
        finally:
            temp_py_file.unlink()
        
        # Test unsupported file type
        test_text = 'This is a text file'
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_text)
            temp_txt_file = Path(f.name)
        
        try:
            chunks = parser.parse_file(temp_txt_file)
            if chunks:
                print("✅ PASS: Universal parser handles unsupported files")
            else:
                print("❌ FAIL: Universal parser failed on unsupported files")
                return False
        finally:
            temp_txt_file.unlink()
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Universal parser test failed: {e}")
        return False

def test_parser_error_handling():
    """Test parser error handling."""
    print("\nTEST 2.5: Parser Error Handling")
    print("=" * 40)
    
    try:
        from core.code_parser import PythonParser
        
        parser = PythonParser()
        
        # Test with non-existent file
        fake_file = Path("non_existent_file.py")
        chunks = parser.parse_file(fake_file)
        
        if chunks == []:
            print("✅ PASS: Parser handles non-existent files gracefully")
        else:
            print("❌ FAIL: Parser should return empty list for non-existent files")
            return False
        
        # Test with invalid Python syntax
        invalid_code = 'def invalid_syntax(\n    pass'
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(invalid_code)
            temp_file = Path(f.name)
        
        try:
            chunks = parser.parse_file(temp_file)
            if chunks == []:
                print("✅ PASS: Parser handles invalid syntax gracefully")
            else:
                print("❌ FAIL: Parser should return empty list for invalid syntax")
                return False
        finally:
            temp_file.unlink()
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Parser error handling test failed: {e}")
        return False

def run_code_parser_tests():
    """Run all code parser tests."""
    print("CODE PARSER TESTING")
    print("=" * 50)
    print("Testing core/code_parser.py functionality...")
    print()
    
    tests = [
        ("Parser Import", test_parser_imports),
        ("CodeChunk Creation", test_code_chunk_creation),
        ("Python Parser Basic", test_python_parser_basic),
        ("Universal Parser", test_universal_parser),
        ("Error Handling", test_parser_error_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("CODE PARSER TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / len(results)) * 100
    print(f"\nCode Parser Success Rate: {passed}/{len(results)} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("\n🎉 EXCELLENT! Code parser is working perfectly!")
        print("Ready to move to next component: Analyzer")
    elif success_rate >= 75:
        print("\n👍 GOOD! Code parser mostly works with minor issues.")
    else:
        print("\n❌ POOR! Code parser has significant issues that need fixing.")
    
    return success_rate

if __name__ == "__main__":
    success_rate = run_code_parser_tests()
    
    print(f"\n🎯 CODE PARSER TEST VERDICT:")
    if success_rate >= 90:
        print("Code parser is SOLID and ready for use!")
    else:
        print("Code parser needs FIXES before proceeding.")
    
    print(f"Test Score: {success_rate:.1f}%")
