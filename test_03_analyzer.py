"""
TEST 3: Analyzer Testing
Test analyzer functionality and dependencies.
"""

import tempfile
from pathlib import Path

def test_analyzer_imports():
    """Test that analyzer imports correctly."""
    print("TEST 3.1: Analyzer Import")
    print("=" * 40)
    
    try:
        from core.analyzer import CodebaseAnalyzer
        print("✅ PASS: CodebaseAnalyzer imports successfully")
        return True
    except ImportError as e:
        print(f"❌ FAIL: CodebaseAnalyzer import failed: {e}")
        print(f"   Missing dependency: {str(e).split()[-1] if 'No module named' in str(e) else 'unknown'}")
        return False
    except Exception as e:
        print(f"❌ FAIL: CodebaseAnalyzer import failed with error: {e}")
        return False

def test_analyzer_creation():
    """Test analyzer object creation."""
    print("\nTEST 3.2: Analyzer Creation")
    print("=" * 40)
    
    try:
        from core.analyzer import CodebaseAnalyzer
        
        analyzer = CodebaseAnalyzer()
        print("✅ PASS: CodebaseAnalyzer object created successfully")
        
        # Check if basic attributes exist
        required_attrs = ['parser', 'stats']
        for attr in required_attrs:
            if hasattr(analyzer, attr):
                print(f"✅ PASS: {attr} attribute exists")
            else:
                print(f"❌ FAIL: {attr} attribute missing")
                return False
        
        # Check stats structure
        if isinstance(analyzer.stats, dict):
            print("✅ PASS: stats is a dictionary")
            
            expected_keys = ['files_processed', 'chunks_extracted', 'embeddings_generated', 'files_skipped', 'errors']
            for key in expected_keys:
                if key in analyzer.stats:
                    print(f"✅ PASS: stats has {key}")
                else:
                    print(f"❌ FAIL: stats missing {key}")
                    return False
        else:
            print("❌ FAIL: stats is not a dictionary")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Analyzer creation failed: {e}")
        return False

def test_file_filtering():
    """Test file filtering logic."""
    print("\nTEST 3.3: File Filtering Logic")
    print("=" * 40)
    
    try:
        from core.analyzer import CodebaseAnalyzer
        
        analyzer = CodebaseAnalyzer()
        
        # Create test files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create various test files
            test_files = [
                'test.py',      # Should be processed
                'test.js',      # Should be processed  
                'test.txt',     # Should NOT be processed (not in SUPPORTED_EXTENSIONS)
                'test.pyc',     # Should NOT be processed (in IGNORE_PATTERNS)
                '__pycache__',  # Should NOT be processed (directory in IGNORE_PATTERNS)
            ]
            
            created_files = []
            for filename in test_files:
                if filename == '__pycache__':
                    # Create directory
                    (temp_path / filename).mkdir()
                else:
                    # Create file
                    file_path = temp_path / filename
                    file_path.write_text("test content")
                    created_files.append(file_path)
            
            # Test _should_process_file method
            py_file = temp_path / 'test.py'
            if analyzer._should_process_file(py_file):
                print("✅ PASS: .py files should be processed")
            else:
                print("❌ FAIL: .py files should be processed")
                return False
            
            js_file = temp_path / 'test.js'
            if analyzer._should_process_file(js_file):
                print("✅ PASS: .js files should be processed")
            else:
                print("❌ FAIL: .js files should be processed")
                return False
            
            txt_file = temp_path / 'test.txt'
            if not analyzer._should_process_file(txt_file):
                print("✅ PASS: .txt files should NOT be processed")
            else:
                print("❌ FAIL: .txt files should NOT be processed")
                return False
            
            pyc_file = temp_path / 'test.pyc'
            if not analyzer._should_process_file(pyc_file):
                print("✅ PASS: .pyc files should NOT be processed")
            else:
                print("❌ FAIL: .pyc files should NOT be processed")
                return False
            
            # Test _should_ignore_path method
            pycache_dir = temp_path / '__pycache__'
            if analyzer._should_ignore_path(pycache_dir):
                print("✅ PASS: __pycache__ directories should be ignored")
            else:
                print("❌ FAIL: __pycache__ directories should be ignored")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: File filtering test failed: {e}")
        return False

def test_file_discovery():
    """Test file discovery functionality."""
    print("\nTEST 3.4: File Discovery")
    print("=" * 40)
    
    try:
        from core.analyzer import CodebaseAnalyzer
        
        analyzer = CodebaseAnalyzer()
        
        # Create test directory structure
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create files and directories
            (temp_path / 'main.py').write_text("print('hello')")
            (temp_path / 'utils.js').write_text("console.log('hello')")
            (temp_path / 'readme.txt').write_text("This is a readme")
            
            # Create subdirectory
            sub_dir = temp_path / 'subdir'
            sub_dir.mkdir()
            (sub_dir / 'helper.py').write_text("def helper(): pass")
            
            # Create ignored directory
            ignored_dir = temp_path / '__pycache__'
            ignored_dir.mkdir()
            (ignored_dir / 'cached.pyc').write_text("cached")
            
            # Test recursive discovery
            files = analyzer._discover_files(temp_path, recursive=True)
            
            # Should find main.py, utils.js, helper.py (but not readme.txt or cached.pyc)
            file_names = [f.name for f in files]
            
            if 'main.py' in file_names:
                print("✅ PASS: Found main.py")
            else:
                print("❌ FAIL: main.py not found")
                return False
            
            if 'utils.js' in file_names:
                print("✅ PASS: Found utils.js")
            else:
                print("❌ FAIL: utils.js not found")
                return False
            
            if 'helper.py' in file_names:
                print("✅ PASS: Found helper.py in subdirectory")
            else:
                print("❌ FAIL: helper.py not found in subdirectory")
                return False
            
            if 'readme.txt' not in file_names:
                print("✅ PASS: Correctly ignored readme.txt")
            else:
                print("❌ FAIL: Should have ignored readme.txt")
                return False
            
            if 'cached.pyc' not in file_names:
                print("✅ PASS: Correctly ignored cached.pyc")
            else:
                print("❌ FAIL: Should have ignored cached.pyc")
                return False
            
            # Test non-recursive discovery
            files_non_recursive = analyzer._discover_files(temp_path, recursive=False)
            file_names_non_recursive = [f.name for f in files_non_recursive]
            
            if 'helper.py' not in file_names_non_recursive:
                print("✅ PASS: Non-recursive mode doesn't find subdirectory files")
            else:
                print("❌ FAIL: Non-recursive mode should not find subdirectory files")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: File discovery test failed: {e}")
        return False

def test_stats_reset():
    """Test statistics reset functionality."""
    print("\nTEST 3.5: Stats Reset")
    print("=" * 40)
    
    try:
        from core.analyzer import CodebaseAnalyzer
        
        analyzer = CodebaseAnalyzer()
        
        # Modify stats
        analyzer.stats['files_processed'] = 10
        analyzer.stats['chunks_extracted'] = 50
        analyzer.stats['errors'] = 2
        
        # Reset stats
        analyzer._reset_stats()
        
        # Check if all stats are reset to 0
        for key, value in analyzer.stats.items():
            if value == 0:
                print(f"✅ PASS: {key} reset to 0")
            else:
                print(f"❌ FAIL: {key} not reset (value: {value})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Stats reset test failed: {e}")
        return False

def run_analyzer_tests():
    """Run all analyzer tests."""
    print("ANALYZER TESTING")
    print("=" * 50)
    print("Testing core/analyzer.py functionality...")
    print()
    
    tests = [
        ("Analyzer Import", test_analyzer_imports),
        ("Analyzer Creation", test_analyzer_creation),
        ("File Filtering", test_file_filtering),
        ("File Discovery", test_file_discovery),
        ("Stats Reset", test_stats_reset)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("ANALYZER TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / len(results)) * 100
    print(f"\nAnalyzer Success Rate: {passed}/{len(results)} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("\n🎉 EXCELLENT! Analyzer is working perfectly!")
        print("Ready to move to next component: Crypto")
    elif success_rate >= 75:
        print("\n👍 GOOD! Analyzer mostly works with minor issues.")
    else:
        print("\n❌ POOR! Analyzer has significant issues that need fixing.")
    
    return success_rate

if __name__ == "__main__":
    success_rate = run_analyzer_tests()
    
    print(f"\n🎯 ANALYZER TEST VERDICT:")
    if success_rate >= 90:
        print("Analyzer is SOLID and ready for use!")
    else:
        print("Analyzer needs FIXES before proceeding.")
    
    print(f"Test Score: {success_rate:.1f}%")
