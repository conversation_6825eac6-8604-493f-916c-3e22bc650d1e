"""
REAL DEEP TEST 3: Analyzer Testing
Test analyzer functionality with real code and show actual outputs.
"""

import tempfile
from pathlib import Path
import traceback

def test_analyzer_imports():
    """Test that analyzer imports correctly."""
    print("TEST 3.1: Analyzer Import")
    print("=" * 40)

    try:
        from core.analyzer import CodebaseAnalyzer
        print("✅ PASS: CodebaseAnalyzer imports successfully")
        return True
    except ImportError as e:
        print(f"❌ FAIL: CodebaseAnalyzer import failed: {e}")
        print(f"   Missing dependency: {str(e).split()[-1] if 'No module named' in str(e) else 'unknown'}")
        return False
    except Exception as e:
        print(f"❌ FAIL: CodebaseAnalyzer import failed with error: {e}")
        traceback.print_exc()
        return False

def test_real_code_analysis():
    """Test analyzer on real Python code and show actual outputs."""
    print("\nTEST 3.2: Real Code Analysis")
    print("=" * 40)

    try:
        from core.analyzer import CodebaseAnalyzer

        # Create a realistic Python file to analyze
        real_python_code = '''"""
A sample Python module for testing the analyzer.
This contains various code constructs to test parsing.
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Optional

class DataProcessor:
    """A class that processes data with various methods."""

    def __init__(self, name: str, config: Dict = None):
        """Initialize the data processor.

        Args:
            name: The name of the processor
            config: Optional configuration dictionary
        """
        self.name = name
        self.config = config or {}
        self.processed_count = 0

    def process_data(self, data: List[str]) -> List[str]:
        """Process a list of data items.

        Args:
            data: List of strings to process

        Returns:
            List of processed strings
        """
        results = []
        for item in data:
            if item and len(item) > 0:
                processed = self._clean_item(item)
                if processed:
                    results.append(processed.upper())
                    self.processed_count += 1
        return results

    def _clean_item(self, item: str) -> Optional[str]:
        """Clean a single data item."""
        if not item:
            return None

        # Remove whitespace and special characters
        cleaned = item.strip()
        if len(cleaned) < 2:
            return None

        return cleaned

    def get_stats(self) -> Dict[str, int]:
        """Get processing statistics."""
        return {
            'processed_count': self.processed_count,
            'name_length': len(self.name)
        }

def process_file(file_path: Path) -> bool:
    """Process a single file.

    Args:
        file_path: Path to the file to process

    Returns:
        True if successful, False otherwise
    """
    try:
        if not file_path.exists():
            print(f"File not found: {file_path}")
            return False

        with open(file_path, 'r') as f:
            content = f.read()

        processor = DataProcessor("file_processor")
        lines = content.splitlines()
        results = processor.process_data(lines)

        print(f"Processed {len(results)} lines from {file_path}")
        return True

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to demonstrate the module."""
    print("Starting data processing demo...")

    # Create sample data
    sample_data = ["  hello  ", "world", "", "test", "a"]

    # Process with DataProcessor
    processor = DataProcessor("demo_processor", {"debug": True})
    results = processor.process_data(sample_data)

    print(f"Results: {results}")
    print(f"Stats: {processor.get_stats()}")

if __name__ == "__main__":
    main()
'''

        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(real_python_code)
            temp_file = Path(f.name)

        try:
            print(f"📁 Created test file: {temp_file.name}")
            print(f"📊 File size: {len(real_python_code)} characters")

            # Create analyzer and analyze the file
            analyzer = CodebaseAnalyzer()
            print("✅ Analyzer created successfully")

            # Test single file analysis
            print("\n🔍 ANALYZING SINGLE FILE:")
            print("-" * 30)

            result = analyzer.analyze_single_file(temp_file)

            print("📋 ANALYSIS RESULTS:")
            print(f"   File path: {result.get('file_path', 'N/A')}")
            print(f"   Chunks found: {result.get('chunks', 0)}")
            print(f"   Fingerprints: {result.get('fingerprints', 0)}")
            print(f"   Embeddings: {result.get('embeddings', 0)}")

            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
                return False

            # Show detailed chunk information
            if 'chunk_details' in result:
                print(f"\n📝 CHUNK DETAILS ({len(result['chunk_details'])} chunks):")
                for i, chunk in enumerate(result['chunk_details'], 1):
                    print(f"   {i}. {chunk['type']}: {chunk['name']}")
                    print(f"      Lines: {chunk['lines']}")
                    print(f"      Complexity: {chunk['complexity']}")
                    print(f"      Content length: {chunk['content_length']} chars")
                    print()

            return True

        finally:
            # Clean up
            temp_file.unlink()

    except Exception as e:
        print(f"❌ FAIL: Real code analysis failed: {e}")
        traceback.print_exc()
        return False

def test_directory_analysis():
    """Test analyzer on a real directory structure and show outputs."""
    print("\nTEST 3.3: Directory Analysis")
    print("=" * 40)

    try:
        from core.analyzer import CodebaseAnalyzer

        # Create a realistic directory structure
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create multiple Python files
            files_to_create = {
                'main.py': '''#!/usr/bin/env python3
"""Main application entry point."""

import sys
from utils import helper_function
from models.user import User

def main():
    """Main function."""
    print("Starting application...")
    user = User("John", 25)
    result = helper_function(user.name)
    print(f"Result: {result}")

if __name__ == "__main__":
    main()
''',
                'utils.py': '''"""Utility functions for the application."""

def helper_function(name: str) -> str:
    """Helper function that processes names."""
    return f"Hello, {name.upper()}!"

def calculate_age_in_days(years: int) -> int:
    """Calculate age in days."""
    return years * 365

class Logger:
    """Simple logging class."""

    def __init__(self, level: str = "INFO"):
        self.level = level

    def log(self, message: str):
        """Log a message."""
        print(f"[{self.level}] {message}")
''',
                'models/user.py': '''"""User model definition."""

from typing import Optional

class User:
    """User class representing a person."""

    def __init__(self, name: str, age: int, email: Optional[str] = None):
        """Initialize user."""
        self.name = name
        self.age = age
        self.email = email

    def get_info(self) -> dict:
        """Get user information."""
        return {
            "name": self.name,
            "age": self.age,
            "email": self.email
        }

    def is_adult(self) -> bool:
        """Check if user is an adult."""
        return self.age >= 18
''',
                'config.json': '''{"debug": true, "version": "1.0"}''',
                'README.md': '''# Test Project\nThis is a test project.''',
                '__pycache__/cache.pyc': '''cached content''',
                '.git/config': '''git config'''
            }

            # Create directory structure and files
            for file_path, content in files_to_create.items():
                full_path = temp_path / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                full_path.write_text(content)

            print(f"📁 Created test directory: {temp_path}")
            print(f"📊 Created {len(files_to_create)} files")

            # Analyze the directory
            analyzer = CodebaseAnalyzer()
            print("✅ Analyzer created successfully")

            print("\n🔍 ANALYZING DIRECTORY:")
            print("-" * 30)

            result = analyzer.analyze_path(temp_path, recursive=True)

            print("📋 DIRECTORY ANALYSIS RESULTS:")
            print(f"   Files processed: {result.get('files_processed', 0)}")
            print(f"   Files skipped: {result.get('files_skipped', 0)}")
            print(f"   Chunks extracted: {result.get('chunks_extracted', 0)}")
            print(f"   Embeddings generated: {result.get('embeddings_generated', 0)}")
            print(f"   Errors: {result.get('errors', 0)}")
            print(f"   Merkle hash: {result.get('merkle_hash', 'N/A')}")

            # Show changes
            if 'changes' in result:
                changes = result['changes']
                print(f"\n📝 CHANGES DETECTED:")
                print(f"   Added: {len(changes.get('added', []))}")
                print(f"   Modified: {len(changes.get('modified', []))}")
                print(f"   Deleted: {len(changes.get('deleted', []))}")
                print(f"   Unchanged: {len(changes.get('unchanged', []))}")

                # Show some added chunks
                if changes.get('added'):
                    print(f"\n🆕 FIRST 5 ADDED CHUNKS:")
                    for i, chunk_id in enumerate(changes['added'][:5], 1):
                        print(f"   {i}. {chunk_id}")

            return True

    except Exception as e:
        print(f"❌ FAIL: Directory analysis failed: {e}")
        traceback.print_exc()
        return False

def test_file_discovery():
    """Test file discovery functionality."""
    print("\nTEST 3.4: File Discovery")
    print("=" * 40)

    try:
        from core.analyzer import CodebaseAnalyzer

        analyzer = CodebaseAnalyzer()

        # Create test directory structure
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create files and directories
            (temp_path / 'main.py').write_text("print('hello')")
            (temp_path / 'utils.js').write_text("console.log('hello')")
            (temp_path / 'readme.txt').write_text("This is a readme")

            # Create subdirectory
            sub_dir = temp_path / 'subdir'
            sub_dir.mkdir()
            (sub_dir / 'helper.py').write_text("def helper(): pass")

            # Create ignored directory
            ignored_dir = temp_path / '__pycache__'
            ignored_dir.mkdir()
            (ignored_dir / 'cached.pyc').write_text("cached")

            # Test recursive discovery
            files = analyzer._discover_files(temp_path, recursive=True)

            # Should find main.py, utils.js, helper.py (but not readme.txt or cached.pyc)
            file_names = [f.name for f in files]

            if 'main.py' in file_names:
                print("✅ PASS: Found main.py")
            else:
                print("❌ FAIL: main.py not found")
                return False

            if 'utils.js' in file_names:
                print("✅ PASS: Found utils.js")
            else:
                print("❌ FAIL: utils.js not found")
                return False

            if 'helper.py' in file_names:
                print("✅ PASS: Found helper.py in subdirectory")
            else:
                print("❌ FAIL: helper.py not found in subdirectory")
                return False

            if 'readme.txt' not in file_names:
                print("✅ PASS: Correctly ignored readme.txt")
            else:
                print("❌ FAIL: Should have ignored readme.txt")
                return False

            if 'cached.pyc' not in file_names:
                print("✅ PASS: Correctly ignored cached.pyc")
            else:
                print("❌ FAIL: Should have ignored cached.pyc")
                return False

            # Test non-recursive discovery
            files_non_recursive = analyzer._discover_files(temp_path, recursive=False)
            file_names_non_recursive = [f.name for f in files_non_recursive]

            if 'helper.py' not in file_names_non_recursive:
                print("✅ PASS: Non-recursive mode doesn't find subdirectory files")
            else:
                print("❌ FAIL: Non-recursive mode should not find subdirectory files")
                return False

        return True

    except Exception as e:
        print(f"❌ FAIL: File discovery test failed: {e}")
        return False

def test_stats_reset():
    """Test statistics reset functionality."""
    print("\nTEST 3.5: Stats Reset")
    print("=" * 40)

    try:
        from core.analyzer import CodebaseAnalyzer

        analyzer = CodebaseAnalyzer()

        # Modify stats
        analyzer.stats['files_processed'] = 10
        analyzer.stats['chunks_extracted'] = 50
        analyzer.stats['errors'] = 2

        # Reset stats
        analyzer._reset_stats()

        # Check if all stats are reset to 0
        for key, value in analyzer.stats.items():
            if value == 0:
                print(f"✅ PASS: {key} reset to 0")
            else:
                print(f"❌ FAIL: {key} not reset (value: {value})")
                return False

        return True

    except Exception as e:
        print(f"❌ FAIL: Stats reset test failed: {e}")
        return False

def run_analyzer_tests():
    """Run all analyzer tests."""
    print("ANALYZER TESTING")
    print("=" * 50)
    print("Testing core/analyzer.py functionality...")
    print()

    tests = [
        ("Analyzer Import", test_analyzer_imports),
        ("Analyzer Creation", test_analyzer_creation),
        ("File Filtering", test_file_filtering),
        ("File Discovery", test_file_discovery),
        ("Stats Reset", test_stats_reset)
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("ANALYZER TEST RESULTS")
    print("=" * 50)

    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1

    success_rate = (passed / len(results)) * 100
    print(f"\nAnalyzer Success Rate: {passed}/{len(results)} ({success_rate:.1f}%)")

    if success_rate == 100:
        print("\n🎉 EXCELLENT! Analyzer is working perfectly!")
        print("Ready to move to next component: Crypto")
    elif success_rate >= 75:
        print("\n👍 GOOD! Analyzer mostly works with minor issues.")
    else:
        print("\n❌ POOR! Analyzer has significant issues that need fixing.")

    return success_rate

if __name__ == "__main__":
    success_rate = run_analyzer_tests()

    print(f"\n🎯 ANALYZER TEST VERDICT:")
    if success_rate >= 90:
        print("Analyzer is SOLID and ready for use!")
    else:
        print("Analyzer needs FIXES before proceeding.")

    print(f"Test Score: {success_rate:.1f}%")
