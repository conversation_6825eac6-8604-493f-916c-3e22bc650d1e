"""
REAL DEEP TEST 4: Crypto Module Testing
Test cryptographic functionality with real data and show actual outputs.
"""

import traceback
from pathlib import Path

def test_crypto_imports():
    """Test that crypto module imports correctly."""
    print("TEST 4.1: Crypto Import")
    print("=" * 40)
    
    try:
        from core.crypto import PathEncryption, generate_chunk_id
        print("✅ PASS: PathEncryption imports successfully")
        print("✅ PASS: generate_chunk_id imports successfully")
        return True
    except ImportError as e:
        print(f"❌ FAIL: Crypto import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ FAIL: Crypto import failed with error: {e}")
        traceback.print_exc()
        return False

def test_path_encryption_real():
    """Test path encryption with real file paths and show outputs."""
    print("\nTEST 4.2: Real Path Encryption")
    print("=" * 40)
    
    try:
        from core.crypto import PathEncryption
        
        # Create encryption instance
        encryption = PathEncryption()
        print("✅ PathEncryption instance created")
        
        # Test with real file paths
        test_paths = [
            "/home/<USER>/project/main.py",
            "C:\\Users\\<USER>\\Documents\\code\\utils.py",
            "./relative/path/to/file.js",
            "../parent/directory/config.json",
            "simple_file.txt"
        ]
        
        print(f"\n🔐 TESTING ENCRYPTION/DECRYPTION:")
        print("-" * 30)
        
        for i, original_path in enumerate(test_paths, 1):
            print(f"\n{i}. Original path: {original_path}")
            
            # Encrypt the path
            encrypted = encryption.encrypt_path(original_path)
            print(f"   Encrypted: {encrypted}")
            print(f"   Encrypted length: {len(encrypted)} chars")
            
            # Decrypt the path
            decrypted = encryption.decrypt_path(encrypted)
            print(f"   Decrypted: {decrypted}")
            
            # Verify round-trip
            if original_path == decrypted:
                print("   ✅ Round-trip successful")
            else:
                print("   ❌ Round-trip failed!")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Path encryption test failed: {e}")
        traceback.print_exc()
        return False

def test_content_hashing_real():
    """Test content hashing with real code content."""
    print("\nTEST 4.3: Real Content Hashing")
    print("=" * 40)
    
    try:
        from core.crypto import PathEncryption
        
        encryption = PathEncryption()
        
        # Test with real code content
        test_contents = [
            'def hello_world():\n    print("Hello, World!")\n    return True',
            '''class Calculator:
    def __init__(self):
        self.value = 0
    
    def add(self, x):
        self.value += x
        return self.value''',
            'import os\nimport sys\nfrom pathlib import Path',
            '# This is a comment\nprint("Simple script")',
            ''  # Empty content
        ]
        
        print(f"\n🔍 TESTING CONTENT HASHING:")
        print("-" * 30)
        
        hashes = []
        for i, content in enumerate(test_contents, 1):
            print(f"\n{i}. Content ({len(content)} chars):")
            print(f"   Preview: {repr(content[:50])}{'...' if len(content) > 50 else ''}")
            
            # Hash the content
            content_hash = encryption.hash_content(content)
            print(f"   Hash: {content_hash}")
            print(f"   Hash length: {len(content_hash)} chars")
            
            hashes.append(content_hash)
        
        # Test hash uniqueness
        print(f"\n🔍 HASH UNIQUENESS TEST:")
        unique_hashes = set(hashes)
        if len(unique_hashes) == len(hashes):
            print("✅ All hashes are unique")
        else:
            print("❌ Some hashes are duplicated!")
            return False
        
        # Test hash consistency
        print(f"\n🔍 HASH CONSISTENCY TEST:")
        same_content = test_contents[0]
        hash1 = encryption.hash_content(same_content)
        hash2 = encryption.hash_content(same_content)
        
        if hash1 == hash2:
            print("✅ Same content produces same hash")
        else:
            print("❌ Same content produces different hashes!")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Content hashing test failed: {e}")
        traceback.print_exc()
        return False

def test_chunk_id_generation():
    """Test chunk ID generation with real parameters."""
    print("\nTEST 4.4: Chunk ID Generation")
    print("=" * 40)
    
    try:
        from core.crypto import generate_chunk_id
        
        # Test with realistic chunk parameters
        test_cases = [
            {
                'file_path': '/project/src/main.py',
                'start_line': 1,
                'end_line': 10,
                'content_hash': 'abc123def456'
            },
            {
                'file_path': 'utils/helper.js',
                'start_line': 25,
                'end_line': 45,
                'content_hash': 'xyz789uvw012'
            },
            {
                'file_path': 'C:\\Code\\Project\\models\\user.py',
                'start_line': 100,
                'end_line': 150,
                'content_hash': 'hash_value_here'
            }
        ]
        
        print(f"\n🆔 TESTING CHUNK ID GENERATION:")
        print("-" * 30)
        
        chunk_ids = []
        for i, case in enumerate(test_cases, 1):
            print(f"\n{i}. Chunk parameters:")
            print(f"   File: {case['file_path']}")
            print(f"   Lines: {case['start_line']}-{case['end_line']}")
            print(f"   Content hash: {case['content_hash']}")
            
            # Generate chunk ID
            chunk_id = generate_chunk_id(
                case['file_path'],
                case['start_line'],
                case['end_line'],
                case['content_hash']
            )
            
            print(f"   Generated ID: {chunk_id}")
            print(f"   ID length: {len(chunk_id)} chars")
            
            chunk_ids.append(chunk_id)
        
        # Test ID uniqueness
        print(f"\n🔍 CHUNK ID UNIQUENESS TEST:")
        unique_ids = set(chunk_ids)
        if len(unique_ids) == len(chunk_ids):
            print("✅ All chunk IDs are unique")
        else:
            print("❌ Some chunk IDs are duplicated!")
            return False
        
        # Test ID consistency
        print(f"\n🔍 CHUNK ID CONSISTENCY TEST:")
        case = test_cases[0]
        id1 = generate_chunk_id(case['file_path'], case['start_line'], case['end_line'], case['content_hash'])
        id2 = generate_chunk_id(case['file_path'], case['start_line'], case['end_line'], case['content_hash'])
        
        if id1 == id2:
            print("✅ Same parameters produce same chunk ID")
        else:
            print("❌ Same parameters produce different chunk IDs!")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Chunk ID generation test failed: {e}")
        traceback.print_exc()
        return False

def run_crypto_tests():
    """Run all crypto tests."""
    print("CRYPTO MODULE TESTING")
    print("=" * 50)
    print("Testing core/crypto.py functionality with real data...")
    print()
    
    tests = [
        ("Crypto Import", test_crypto_imports),
        ("Path Encryption", test_path_encryption_real),
        ("Content Hashing", test_content_hashing_real),
        ("Chunk ID Generation", test_chunk_id_generation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("CRYPTO TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / len(results)) * 100
    print(f"\nCrypto Success Rate: {passed}/{len(results)} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("\n🎉 EXCELLENT! Crypto module is working perfectly!")
        print("All encryption, hashing, and ID generation functions work correctly.")
    elif success_rate >= 75:
        print("\n👍 GOOD! Crypto module mostly works with minor issues.")
    else:
        print("\n❌ POOR! Crypto module has significant issues that need fixing.")
    
    return success_rate

if __name__ == "__main__":
    success_rate = run_crypto_tests()
    
    print(f"\n🎯 CRYPTO TEST VERDICT:")
    if success_rate >= 90:
        print("Crypto module is SOLID and ready for use!")
        print("Encryption, decryption, hashing, and ID generation all work correctly.")
    else:
        print("Crypto module needs FIXES before proceeding.")
    
    print(f"Test Score: {success_rate:.1f}%")
