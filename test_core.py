#!/usr/bin/env python3
"""
Comprehensive test suite for all core modules.
"""

import sys
import os
sys.path.append('core')

def test_config():
    """Test config.py module."""
    print("🔧 Testing config.py...")
    try:
        import config
        print(f"  ✅ DB_PATH: {config.DB_PATH}")
        print(f"  ✅ Supported extensions: {len(config.SUPPORTED_EXTENSIONS)} types")
        print(f"  ✅ Embedding dimension: {config.EMBEDDING_DIMENSION}")
        print("  ✅ config.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ config.py - FAILED: {e}")
        return False

def test_crypto():
    """Test crypto.py module."""
    print("\n🔐 Testing crypto.py...")
    try:
        import crypto
        # Test path encryption
        path_enc = crypto.PathEncryption()
        test_path = "/test/path/file.py"
        encrypted = path_enc.encrypt_path(test_path)
        decrypted = path_enc.decrypt_path(encrypted)
        print(f"  ✅ Path encryption: {encrypted[:16]}...")
        print(f"  ✅ Path decryption: {decrypted == test_path}")

        # Test content hashing
        test_data = "def hello(): return 'world'"
        hash_result = path_enc.hash_content(test_data)
        print(f"  ✅ Content hash: {hash_result[:16]}...")

        # Test chunk ID generation
        chunk_id = crypto.generate_chunk_id(test_path, 1, 5, hash_result)
        print(f"  ✅ Chunk ID: {chunk_id}")
        print("  ✅ crypto.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ crypto.py - FAILED: {e}")
        return False

def test_fingerprint():
    """Test fingerprint.py module."""
    print("\n🔍 Testing fingerprint.py...")
    try:
        import fingerprint
        # Test fingerprint manager
        fp_manager = fingerprint.FingerprintManager()
        print("  ✅ Fingerprint manager initialized")

        # Test Merkle tree building
        from pathlib import Path
        root_path = Path("core")
        if root_path.exists():
            merkle_root = fp_manager.build_merkle_tree(root_path)
            print(f"  ✅ Merkle tree built: {merkle_root.name}")
        print("  ✅ fingerprint.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ fingerprint.py - FAILED: {e}")
        return False

def test_embeddings():
    """Test embeddings.py module."""
    print("\n🧠 Testing embeddings.py...")
    try:
        import embeddings
        import code_parser
        # Test embedding generation
        emb_gen = embeddings.EmbeddingGenerator()

        # Create a test code chunk
        test_chunk = code_parser.CodeChunk(
            content="def add(a, b): return a + b",
            start_line=1,
            end_line=1,
            chunk_type="function",
            name="add"
        )

        embedding = emb_gen.generate_embedding(test_chunk, "test_chunk_id")
        print(f"  ✅ Embedding shape: {len(embedding.embedding)} dimensions")
        print("  ✅ embeddings.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ embeddings.py - FAILED: {e}")
        return False

def test_storage():
    """Test storage.py module."""
    print("\n💾 Testing storage.py...")
    try:
        import storage
        # Test vector database
        db = storage.VectorDatabase()
        print("  ✅ Vector database initialized")
        print("  ✅ storage.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ storage.py - FAILED: {e}")
        return False

def test_code_parser():
    """Test code_parser.py module."""
    print("\n📝 Testing code_parser.py...")
    try:
        import code_parser
        from pathlib import Path
        # Test code parsing
        parser = code_parser.UniversalParser()

        # Create a temporary test file
        test_file = Path("temp_test.py")
        test_code = """def hello_world():
    '''A simple function'''
    return "Hello, World!"

class TestClass:
    def method(self):
        pass
"""
        with open(test_file, 'w') as f:
            f.write(test_code)

        chunks = parser.parse_file(test_file)
        print(f"  ✅ Parsed {len(chunks)} code chunks")

        # Clean up
        test_file.unlink()
        print("  ✅ code_parser.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ code_parser.py - FAILED: {e}")
        return False

def test_analyzer():
    """Test analyzer.py module."""
    print("\n📊 Testing analyzer.py...")
    try:
        import analyzer
        # Test analyzer
        codebase_analyzer = analyzer.CodebaseAnalyzer()
        print("  ✅ Codebase analyzer initialized")
        print("  ✅ analyzer.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ analyzer.py - FAILED: {e}")
        return False

def test_code_executor():
    """Test code_executor.py module."""
    print("\n🔒 Testing code_executor.py...")
    try:
        import code_executor
        # Test safe execution
        executor = code_executor.SafeCodeExecutor()
        test_code = "result = 2 + 2"
        result = executor.execute_code(test_code)
        print(f"  ✅ Safe execution: {result.success}")
        print("  ✅ code_executor.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ code_executor.py - FAILED: {e}")
        return False

def test_llm_integration():
    """Test llm_integration.py module."""
    print("\n🤖 Testing llm_integration.py...")
    try:
        import llm_integration
        # Test LLM manager
        llm_manager = llm_integration.LLMManager()
        print("  ✅ LLM manager initialized")
        print("  ✅ llm_integration.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ llm_integration.py - FAILED: {e}")
        return False

def test_code_assistant():
    """Test code_assistant.py module."""
    print("\n🧠 Testing code_assistant.py...")
    try:
        import code_assistant
        # Test code assistant
        assistant = code_assistant.IntelligentCodeAssistant()
        print("  ✅ Code assistant initialized")
        print("  ✅ code_assistant.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ code_assistant.py - FAILED: {e}")
        return False

def test_agentic_coder():
    """Test agentic_coder.py module."""
    print("\n🚀 Testing agentic_coder.py...")
    try:
        import agentic_coder
        # Test agentic coder
        coder = agentic_coder.AgenticCoder()
        print("  ✅ Agentic coder initialized")
        print("  ✅ agentic_coder.py - PASSED")
        return True
    except Exception as e:
        print(f"  ❌ agentic_coder.py - FAILED: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 COMPREHENSIVE CORE MODULE TESTING")
    print("=" * 50)

    tests = [
        test_config,
        test_crypto,
        test_fingerprint,
        test_embeddings,
        test_storage,
        test_code_parser,
        test_analyzer,
        test_code_executor,
        test_llm_integration,
        test_code_assistant,
        test_agentic_coder
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 50)
    print(f"🎯 RESULTS: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL CORE MODULES WORKING PERFECTLY!")
    else:
        print(f"⚠️  {total - passed} modules need attention")

    return passed == total

if __name__ == "__main__":
    main()
