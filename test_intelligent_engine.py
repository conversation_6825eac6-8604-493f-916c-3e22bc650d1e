"""
Test the Intelligent Augment Engine with the neural network task that previously failed.
"""

from pathlib import Path
from core.intelligent_augment_engine import IntelligentAugmentEngine

def test_neural_network():
    """Test the neural network generation that previously failed."""
    
    print("🧠 Testing Intelligent Augment Engine with Neural Network Task")
    print("=" * 60)
    
    # Initialize the engine
    engine = IntelligentAugmentEngine()
    
    # The exact prompt that failed before
    prompt = "create a simple neural network architecture to train over some binary classification data. While training it should also show the progress bar."
    
    # Test directory
    test_dir = Path("./examples/demo_project")
    test_dir.mkdir(parents=True, exist_ok=True)
    
    # Run the intelligent engine
    result = engine.augment_code_intelligent(prompt, test_dir)
    
    # Display results
    print("\n" + "=" * 60)
    print("🎯 RESULTS:")
    print("=" * 60)
    
    if result['success']:
        print("✅ SUCCESS!")
        print(f"⏱️  Execution time: {result['execution_time']:.2f} seconds")
        print(f"🔄 Iterations: {result['iterations']}")
        print(f"📊 Plan complexity: {result['plan'].estimated_complexity}/10")
        print(f"🧩 Components: {len(result['plan'].required_components)}")
        
        print("\n📋 Implementation Plan:")
        for i, component in enumerate(result['plan'].implementation_order, 1):
            print(f"  {i}. {component}")
        
        print("\n📤 Output:")
        print(result['output'][:500] + "..." if len(result['output']) > 500 else result['output'])
        
        # Save the generated code
        code_file = test_dir / "intelligent_neural_network.py"
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(result['final_code'])
        print(f"\n💾 Code saved to: {code_file}")
        
    else:
        print("❌ FAILED")
        print(f"Error: {result['output']}")
    
    return result

def test_web_scraper():
    """Test web scraper generation."""
    
    print("\n🌐 Testing Web Scraper Generation")
    print("=" * 40)
    
    engine = IntelligentAugmentEngine()
    prompt = "create a web scraper that fetches content from URLs and extracts titles and links"
    test_dir = Path("./examples/demo_project")
    
    result = engine.augment_code_intelligent(prompt, test_dir)
    
    if result['success']:
        print("✅ Web scraper generated successfully!")
        print(f"⏱️  Time: {result['execution_time']:.2f}s")
        
        # Save the code
        code_file = test_dir / "intelligent_web_scraper.py"
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(result['final_code'])
        print(f"💾 Saved to: {code_file}")
    else:
        print("❌ Web scraper generation failed")
    
    return result

def test_data_analysis():
    """Test data analysis generation."""
    
    print("\n📊 Testing Data Analysis Generation")
    print("=" * 40)
    
    engine = IntelligentAugmentEngine()
    prompt = "create a data analysis tool that loads CSV data, performs basic statistics, and visualizes results"
    test_dir = Path("./examples/demo_project")
    
    result = engine.augment_code_intelligent(prompt, test_dir)
    
    if result['success']:
        print("✅ Data analyzer generated successfully!")
        print(f"⏱️  Time: {result['execution_time']:.2f}s")
        
        # Save the code
        code_file = test_dir / "intelligent_data_analyzer.py"
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(result['final_code'])
        print(f"💾 Saved to: {code_file}")
    else:
        print("❌ Data analyzer generation failed")
    
    return result

if __name__ == "__main__":
    print("🚀 INTELLIGENT AUGMENT ENGINE TESTING")
    print("=" * 50)
    print("Testing the redesigned engine that should handle complex tasks...")
    print()
    
    # Test the neural network task that previously failed
    nn_result = test_neural_network()
    
    # Test other complex tasks
    scraper_result = test_web_scraper()
    data_result = test_data_analysis()
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 FINAL SUMMARY")
    print("=" * 60)
    
    results = [
        ("Neural Network", nn_result['success']),
        ("Web Scraper", scraper_result['success']),
        ("Data Analysis", data_result['success'])
    ]
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    for task, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{task:15}: {status}")
    
    print(f"\n🏆 Overall Success Rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("\n🎉 EXCELLENT! The Intelligent Augment Engine successfully handled all complex tasks!")
        print("This demonstrates the power of:")
        print("  • 📋 Comprehensive task planning")
        print("  • 🔍 Pre-execution validation")
        print("  • 🎨 Complete, self-contained code generation")
        print("  • ⚡ Intelligent error correction")
    else:
        print(f"\n⚠️  {total_count - success_count} task(s) still need improvement.")
        print("The intelligent approach is working but needs refinement.")
