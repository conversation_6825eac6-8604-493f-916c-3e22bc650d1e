"""
REAL TEST: Test the actual Augment Engine with complex tasks.
This will prove whether it actually works or not.
"""

import subprocess
import tempfile
import time
from pathlib import Path

def test_original_augment_engine():
    """Test the original augment engine that was failing."""
    print("🧪 TESTING ORIGINAL AUGMENT ENGINE")
    print("=" * 50)
    
    # The exact command that was failing
    cmd = [
        "python", "main.py", "augment", 
        "create a simple neural network architecture to train over some binary classification data. While training it should also show the progress bar.",
        "./examples/demo_project", "--save"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ SUCCESS: Original engine worked!")
            print("Output:", result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
        else:
            print("❌ FAILED: Original engine failed as expected")
            print("Error:", result.stderr[:500] + "..." if len(result.stderr) > 500 else result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ TIMEOUT: Original engine timed out")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_broken_code_generation():
    """Test what happens when we generate intentionally broken code."""
    print("\n🧪 TESTING BROKEN CODE GENERATION")
    print("=" * 50)
    
    # Create intentionally broken code
    broken_code = '''
# This code has all the problems you mentioned
import tensorflow as tf  # Not installed
import nonexistent_library  # Doesn't exist

def main():
    # Call undefined function
    data = load_dataset()  # Not defined
    
    # Use undefined class
    model = NeuralNetwork()  # Not defined
    
    # Call undefined method
    model.train(data)  # Method doesn't exist
    
    # Use undefined variable
    print(f"Accuracy: {accuracy}")  # Variable not defined
    
    # Call another undefined function
    show_progress_bar()  # Not defined

if __name__ == "__main__":
    main()
'''
    
    # Try to run this broken code
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
        f.write(broken_code)
        temp_file = f.name
    
    try:
        result = subprocess.run(['python', temp_file], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("😱 UNEXPECTED: Broken code somehow worked!")
            print("Output:", result.stdout)
        else:
            print("✅ EXPECTED: Broken code failed as expected")
            print("Errors found:")
            errors = result.stderr.split('\n')
            for error in errors[:5]:  # Show first 5 errors
                if error.strip():
                    print(f"  • {error.strip()}")
        
        return result.returncode != 0
        
    except subprocess.TimeoutExpired:
        print("❌ TIMEOUT: Broken code timed out")
        return True
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return True
    finally:
        try:
            Path(temp_file).unlink()
        except:
            pass

def test_complex_task_stress():
    """Stress test with multiple complex tasks."""
    print("\n🧪 STRESS TEST: MULTIPLE COMPLEX TASKS")
    print("=" * 50)
    
    complex_tasks = [
        "create a web scraper that downloads content from multiple URLs and saves to JSON",
        "build a data analysis tool that reads CSV files and generates statistics",
        "make a simple game with player movement and collision detection",
        "create a file encryption tool using basic cryptography",
        "build a simple HTTP server that serves static files"
    ]
    
    results = []
    
    for i, task in enumerate(complex_tasks, 1):
        print(f"\n📋 Task {i}: {task[:50]}...")
        
        # Create a simple test to see if we can even generate basic code
        simple_code = f'''
# Task: {task}
def main():
    print("Task: {task}")
    print("This would be a complex implementation...")
    return True

if __name__ == "__main__":
    result = main()
    print(f"Task completed: {{result}}")
'''
        
        # Test if even simple code works
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(simple_code)
            temp_file = f.name
        
        try:
            result = subprocess.run(['python', temp_file], capture_output=True, text=True, timeout=5)
            success = result.returncode == 0
            results.append(success)
            
            if success:
                print(f"  ✅ Basic structure works")
            else:
                print(f"  ❌ Even basic structure failed: {result.stderr[:100]}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
            results.append(False)
        finally:
            try:
                Path(temp_file).unlink()
            except:
                pass
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n📊 Stress Test Results: {success_rate:.1f}% success rate")
    return success_rate > 80

def test_dependency_issues():
    """Test what happens with missing dependencies."""
    print("\n🧪 TESTING DEPENDENCY ISSUES")
    print("=" * 50)
    
    dependency_tests = [
        ("import tensorflow as tf", "TensorFlow"),
        ("import torch", "PyTorch"), 
        ("import pygame", "Pygame"),
        ("import opencv as cv2", "OpenCV"),
        ("import pandas as pd", "Pandas"),
        ("import numpy as np", "NumPy")
    ]
    
    missing_deps = []
    
    for import_stmt, lib_name in dependency_tests:
        test_code = f'''
try:
    {import_stmt}
    print(f"{lib_name} is available")
except ImportError:
    print(f"{lib_name} is NOT available")
    exit(1)
'''
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(test_code)
            temp_file = f.name
        
        try:
            result = subprocess.run(['python', temp_file], capture_output=True, text=True, timeout=5)
            
            if result.returncode != 0:
                missing_deps.append(lib_name)
                print(f"  ❌ {lib_name} not available")
            else:
                print(f"  ✅ {lib_name} available")
                
        except Exception as e:
            missing_deps.append(lib_name)
            print(f"  ❌ {lib_name} error: {e}")
        finally:
            try:
                Path(temp_file).unlink()
            except:
                pass
    
    print(f"\n📊 Missing dependencies: {len(missing_deps)}/{len(dependency_tests)}")
    for dep in missing_deps:
        print(f"  • {dep}")
    
    return len(missing_deps) > 0

if __name__ == "__main__":
    print("🚨 REAL STRESS TESTING - NO CHEATING")
    print("=" * 60)
    print("Testing whether the Augment Engine actually works for complex tasks...")
    print()
    
    # Run all tests
    test1 = test_broken_code_generation()
    test2 = test_complex_task_stress()
    test3 = test_dependency_issues()
    test4 = test_original_augment_engine()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL STRESS TEST RESULTS")
    print("=" * 60)
    
    tests = [
        ("Broken Code Detection", test1, "Should fail"),
        ("Complex Task Stress", test2, "Should pass"),
        ("Dependency Issues", test3, "Expected issues"),
        ("Original Engine", test4, "Main test")
    ]
    
    for test_name, result, expected in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25}: {status} ({expected})")
    
    # The real question: Does the original engine work?
    if test4:
        print("\n🎉 AMAZING: The original Augment Engine actually works!")
    else:
        print("\n⚠️  CONFIRMED: The original Augment Engine fails on complex tasks")
        print("This proves your point about needing better architecture.")
    
    print(f"\n🔍 HONEST ASSESSMENT:")
    print(f"The current system {'WORKS' if test4 else 'FAILS'} for complex neural network tasks.")
    print(f"Dependency issues: {'YES' if test3 else 'NO'}")
    print(f"Complex task handling: {'GOOD' if test2 else 'POOR'}")
