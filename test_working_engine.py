"""
Test the WORKING Augment Engine with the exact neural network task that failed before.
"""

from pathlib import Path
from core.working_augment_engine import WorkingAugmentEngine

def test_neural_network_task():
    """Test the exact neural network task that was failing."""
    
    print("🧠 TESTING WORKING AUGMENT ENGINE")
    print("=" * 50)
    print("Testing the EXACT task that failed before:")
    print("'create a simple neural network architecture to train over some binary classification data. While training it should also show the progress bar.'")
    print()
    
    # Initialize the working engine
    engine = WorkingAugmentEngine()
    
    # The exact prompt that was failing
    prompt = "create a simple neural network architecture to train over some binary classification data. While training it should also show the progress bar."
    
    # Test directory
    test_dir = Path("./examples/demo_project")
    test_dir.mkdir(parents=True, exist_ok=True)
    
    # Run the working engine
    result = engine.augment_code(prompt, test_dir)
    
    # Display results
    print("\n" + "=" * 60)
    print("🎯 RESULTS:")
    print("=" * 60)
    
    if result.success:
        print("✅ SUCCESS!")
        print(f"⏱️  Execution time: {result.execution_time:.2f} seconds")
        print(f"🔄 Iterations used: {result.iterations_used}")
        
        print("\n📤 Output (first 1000 chars):")
        print("-" * 50)
        print(result.output[:1000] + "..." if len(result.output) > 1000 else result.output)
        print("-" * 50)
        
        # Save the generated code to a file
        code_file = test_dir / "working_neural_network.py"
        # We need to extract the code from the engine - let's regenerate it
        analysis = engine._analyze_task(prompt)
        generated_code = engine._generate_complete_code(analysis, prompt)
        
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(generated_code)
        print(f"\n💾 Generated code saved to: {code_file}")
        
        return True
        
    else:
        print("❌ FAILED")
        print(f"Error: {result.error}")
        print(f"Output: {result.output}")
        return False

def test_web_scraper_task():
    """Test web scraper generation."""
    
    print("\n🌐 TESTING WEB SCRAPER TASK")
    print("=" * 40)
    
    engine = WorkingAugmentEngine()
    prompt = "create a web scraper that fetches content from URLs and extracts titles and links"
    test_dir = Path("./examples/demo_project")
    
    result = engine.augment_code(prompt, test_dir)
    
    if result.success:
        print("✅ Web scraper SUCCESS!")
        print(f"⏱️  Time: {result.execution_time:.2f}s")
        print(f"🔄 Iterations: {result.iterations_used}")
        
        # Save the code
        analysis = engine._analyze_task(prompt)
        generated_code = engine._generate_complete_code(analysis, prompt)
        code_file = test_dir / "working_web_scraper.py"
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(generated_code)
        print(f"💾 Saved to: {code_file}")
        return True
    else:
        print("❌ Web scraper FAILED")
        print(f"Error: {result.error}")
        return False

def test_data_analysis_task():
    """Test data analysis generation."""
    
    print("\n📊 TESTING DATA ANALYSIS TASK")
    print("=" * 40)
    
    engine = WorkingAugmentEngine()
    prompt = "create a data analysis tool that loads CSV data, performs basic statistics, and visualizes results"
    test_dir = Path("./examples/demo_project")
    
    result = engine.augment_code(prompt, test_dir)
    
    if result.success:
        print("✅ Data analysis SUCCESS!")
        print(f"⏱️  Time: {result.execution_time:.2f}s")
        print(f"🔄 Iterations: {result.iterations_used}")
        
        # Save the code
        analysis = engine._analyze_task(prompt)
        generated_code = engine._generate_complete_code(analysis, prompt)
        code_file = test_dir / "working_data_analyzer.py"
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(generated_code)
        print(f"💾 Saved to: {code_file}")
        return True
    else:
        print("❌ Data analysis FAILED")
        print(f"Error: {result.error}")
        return False

def test_game_task():
    """Test game generation."""
    
    print("\n🎮 TESTING GAME TASK")
    print("=" * 30)
    
    engine = WorkingAugmentEngine()
    prompt = "create a simple game with player movement and collision detection"
    test_dir = Path("./examples/demo_project")
    
    result = engine.augment_code(prompt, test_dir)
    
    if result.success:
        print("✅ Game SUCCESS!")
        print(f"⏱️  Time: {result.execution_time:.2f}s")
        print(f"🔄 Iterations: {result.iterations_used}")
        
        # Save the code
        analysis = engine._analyze_task(prompt)
        generated_code = engine._generate_complete_code(analysis, prompt)
        code_file = test_dir / "working_game.py"
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(generated_code)
        print(f"💾 Saved to: {code_file}")
        return True
    else:
        print("❌ Game FAILED")
        print(f"Error: {result.error}")
        return False

def run_comprehensive_test():
    """Run comprehensive tests of the working engine."""
    
    print("🚀 COMPREHENSIVE WORKING ENGINE TEST")
    print("=" * 60)
    print("Testing the redesigned engine that should actually work...")
    print()
    
    # Test all complex tasks
    results = []
    
    # 1. Neural Network (the main failing task)
    results.append(("Neural Network", test_neural_network_task()))
    
    # 2. Web Scraper
    results.append(("Web Scraper", test_web_scraper_task()))
    
    # 3. Data Analysis
    results.append(("Data Analysis", test_data_analysis_task()))
    
    # 4. Game Development
    results.append(("Game", test_game_task()))
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 COMPREHENSIVE TEST RESULTS")
    print("=" * 60)
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    for task, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{task:15}: {status}")
    
    success_rate = (success_count / total_count) * 100
    print(f"\n🏆 Overall Success Rate: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    if success_count == total_count:
        print("\n🎉 PERFECT! The Working Augment Engine handles ALL complex tasks!")
        print("\n🚀 KEY ACHIEVEMENTS:")
        print("  ✅ Neural networks with progress bars")
        print("  ✅ Web scraping with URL handling")
        print("  ✅ Data analysis with statistics")
        print("  ✅ Game development with collision detection")
        print("  ✅ All using standard library only")
        print("  ✅ Complete, self-contained implementations")
        print("  ✅ Intelligent error correction")
        print("\n🎯 This proves the redesigned architecture works!")
        
    elif success_count >= total_count * 0.75:
        print(f"\n🎊 EXCELLENT! {success_rate:.1f}% success rate!")
        print("The working engine significantly outperforms the original.")
        
    else:
        print(f"\n⚠️  NEEDS IMPROVEMENT: {success_rate:.1f}% success rate")
        print("Still better than the original, but needs more work.")
    
    return success_rate

if __name__ == "__main__":
    success_rate = run_comprehensive_test()
    
    print(f"\n" + "=" * 60)
    print("🔍 FINAL ASSESSMENT")
    print("=" * 60)
    
    if success_rate == 100:
        print("🏆 MISSION ACCOMPLISHED!")
        print("The Working Augment Engine successfully handles complex tasks.")
        print("This is a REAL, FUNCTIONAL AI coding assistant!")
    elif success_rate >= 75:
        print("🎯 MAJOR SUCCESS!")
        print("The Working Augment Engine significantly outperforms the original.")
        print("This demonstrates the power of proper architecture.")
    else:
        print("⚠️  PARTIAL SUCCESS")
        print("Better than the original, but still needs refinement.")
    
    print(f"\nSuccess Rate: {success_rate:.1f}%")
    print("Generated code files are saved in ./examples/demo_project/")
